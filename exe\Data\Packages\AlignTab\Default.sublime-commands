[{"caption": "AlignTab", "command": "align_tab"}, {"caption": "AlignTab: Table Mode", "command": "align_tab", "args": {"mode": true}}, {"caption": "AlignTab: Live Preview Mode", "command": "align_tab", "args": {"live_preview": true}}, {"caption": "AlignTab: Exit Table Mode", "command": "align_tab_clear_mode"}, {"caption": "Preferences: <PERSON><PERSON><PERSON><PERSON>", "command": "edit_settings", "args": {"base_file": "${packages}/AlignTab/AlignTab.sublime-settings", "default": "{\n\t$0\n}\n"}}, {"caption": "Preferences: AlignTab Context Menu", "command": "align_tab_edit_settings", "args": {"base_file": "${packages}/AlignTab/Context.sublime-menu", "default": "[\n\t$0\n]\n"}}]