[{"id": "edit", "children": [{"id": "line", "children": [{"command": "delete_blank_lines", "id": "delete-blank-lines-no_s", "caption": "Delete blank lines", "args": {"surplus": false}, "keys": ["ctrl+alt+backspace"]}, {"command": "delete_blank_lines", "id": "delete-blank-lines_s", "caption": "Delete Surplus blank lines", "args": {"surplus": true}, "keys": ["ctrl+alt+shift+backspace"]}]}]}, {"caption": "Preferences", "mnemonic": "n", "id": "preferences", "children": [{"caption": "Package Settings", "mnemonic": "P", "id": "package-settings", "children": [{"caption": "DeleteBlankLines", "children": [{"caption": "-"}, {"command": "open_file", "args": {"file": "${packages}/DeleteBlankLines/DeleteBlankLines.sublime-settings"}, "caption": "<PERSON>ting<PERSON> <PERSON> <PERSON><PERSON><PERSON>"}, {"command": "open_file", "args": {"file": "${packages}/User/DeleteBlankLines.sublime-settings"}, "caption": "Settings – User"}, {"caption": "-"}, {"args": {"file": "${packages}/DeleteBlankLines/Default (Windows).sublime-keymap", "platform": "Windows"}, "caption": "Key Bindings – <PERSON><PERSON><PERSON>", "command": "open_file"}, {"args": {"file": "${packages}/DeleteBlankLines/Default (OSX).sublime-keymap", "platform": "OSX"}, "caption": "Key Bindings – <PERSON><PERSON><PERSON>", "command": "open_file"}, {"args": {"file": "${packages}/DeleteBlankLines/Default (Linux).sublime-keymap", "platform": "Linux"}, "caption": "Key Bindings – <PERSON><PERSON><PERSON>", "command": "open_file"}, {"args": {"file": "${packages}/User/Default (Windows).sublime-keymap", "platform": "Windows"}, "caption": "Key Bindings – User", "command": "open_file"}, {"args": {"file": "${packages}/User/Default (OSX).sublime-keymap", "platform": "OSX"}, "caption": "Key Bindings – User", "command": "open_file"}, {"args": {"file": "${packages}/User/Default (Linux).sublime-keymap", "platform": "Linux"}, "caption": "Key Bindings – User", "command": "open_file"}, {"caption": "-"}]}]}]}]