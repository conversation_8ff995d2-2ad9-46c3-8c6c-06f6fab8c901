{
	"name": "ayu",
	"globals": {
		"background": "#10141c",
		"foreground": "#bfbdb6",
		"invisibles": "#bfbdb64d",
		"caret": "#e6b450",
		"block_caret": "#e6b4504d",
		"line_highlight": "#161a24",
		"accent": "#e6b450",
		"popup_css": "\n      html, body {\n        background-color: #141821;\n        color: #bfbdb6;\n        --mdpopups-font-mono: \"PragmataPro Mono Liga\", \"sf mono\", <PERSON><PERSON>as, \"Liberation Mono\", Menlo, Courier, monospace;\n        --mdpopups-bg: #141821;\n        --mdpopups-link: #59c2ff;\n      }\n      body {\n        padding: 1px 3px;\n      }\n      a {\n        color: rgba(89,194,255, .7);\n      }\n    ",
		"gutter": "#10141c",
		"gutter_foreground": "#6c738099",
		"line_diff_width": "2",
		"line_diff_added": "#7fd962b3",
		"line_diff_modified": "#73b8ffb3",
		"line_diff_deleted": "#f26d78b3",
		"selection": "#3388ff40",
		"selection_border": "#3388ff40",
		"selection_border_width": "1",
		"inactive_selection": "#80b5ff26",
		"inactive_selection_border": "#80b5ff26",
		"selection_corner_style": "round",
		"selection_corner_radius": "4",
		"highlight": "#e6b45066",
		"find_highlight": "#e6b450",
		"find_highlight_foreground": "#10141c",
		"guide": "#6c73804d",
		"active_guide": "#6c738080",
		"stack_guide": "#6c738066",
		"shadow": "#10141c4d",
		"shadow_width": "3",
        "misspelling": "#bb0000",
        "fold_marker": "hsla(0, 0%, 50%, 0.5",
	},
	"rules": [
		{
			"name": "Comment",
			"scope": "comment",
			"font_style": "italic",
			"foreground": "#acb6bf8c"
		},
		{
			"name": "String",
			"scope": "string - meta.template, constant.other.symbol, string.quoted",
			"foreground": "#aad94c"
		},
		{
			"name": "Regular Expressions and Escape Characters",
			"scope": "string.regexp, constant.character, constant.other",
			"foreground": "#95e6cb"
		},
		{
			"name": "Number",
			"scope": "constant.numeric",
			"foreground": "#e6b450"
		},
		{
			"name": "Built-in constants",
			"scope": "constant.language",
			"foreground": "#e6b450"
		},
		{
			"name": "Constants",
			"scope": "meta.constant, entity.name.constant",
			"foreground": "#d2a6ff"
		},
		{
			"name": "Variable",
			"scope": "variable",
			"foreground": "#bfbdb6"
		},
		{
			"name": "Member Variable",
			"scope": "variable.member",
			"foreground": "#f07178"
		},
		{
			"name": "Language variable",
			"scope": "variable.language",
			"font_style": "italic",
			"foreground": "#39bae6"
		},
		{
			"name": "Storage",
			"scope": "storage, storage.type.keyword",
			"foreground": "#ff8f40"
		},
		{
			"name": "Keyword",
			"scope": "keyword",
			"foreground": "#ff8f40"
		},
		{
			"name": "Java keyword fixes",
			"scope": "source.java meta.class.java meta.class.identifier.java storage.type.java",
			"foreground": "#ff8f40"
		},
		{
			"name": "Operators",
			"scope": "keyword.operator",
			"foreground": "#f29668"
		},
		{
			"name": "Separators like ; or ,",
			"scope": "punctuation.separator, punctuation.terminator",
			"foreground": "#bfbdb6b3"
		},
		{
			"name": "Punctuation",
			"scope": "punctuation.section",
			"foreground": "#bfbdb6"
		},
		{
			"name": "Accessor",
			"scope": "punctuation.accessor",
			"foreground": "#f29668"
		},
		{
			"name": "JavaScript/TypeScript interpolation punctuation",
			"scope": "punctuation.definition.template-expression",
			"foreground": "#ff8f40"
		},
		{
			"name": "Ruby interpolation punctuation",
			"scope": "punctuation.section.interpolation",
			"foreground": "#ff8f40"
		},
		{
			"name": "Types fixes",
			"scope": "source.java storage.type, source.haskell storage.type, source.c storage.type, source.zig storage.type",
			"foreground": "#59c2ff"
		},
		{
			"name": "Inherited class type",
			"scope": "entity.other.inherited-class",
			"foreground": "#39bae6"
		},
		{
			"name": "Lambda arrow",
			"scope": "storage.type.function",
			"foreground": "#ff8f40"
		},
		{
			"name": "Java primitive variable types",
			"scope": "source.java storage.type.primitive",
			"foreground": "#39bae6"
		},
		{
			"name": "Function name",
			"scope": "entity.name.function",
			"foreground": "#ffb454"
		},
		{
			"name": "Function arguments",
			"scope": "variable.parameter, meta.parameter",
			"foreground": "#d2a6ff"
		},
		{
			"name": "Function call",
			"scope": "variable.function, variable.annotation, meta.function-call.generic, support.function.go",
			"foreground": "#ffb454"
		},
		{
			"name": "Library function",
			"scope": "support.function, support.macro",
			"foreground": "#f07178"
		},
		{
			"name": "Imports and packages",
			"scope": "entity.name.import, entity.name.package",
			"foreground": "#aad94c"
		},
		{
			"name": "Entity name",
			"scope": "entity.name, source.js meta.function-call.constructor variable.type",
			"foreground": "#59c2ff"
		},
		{
			"name": "Tag",
			"scope": "entity.name.tag, meta.tag.sgml",
			"foreground": "#39bae6"
		},
		{
			"name": "Tag start/end",
			"scope": "punctuation.definition.tag.end, punctuation.definition.tag.begin, punctuation.definition.tag",
			"foreground": "#39bae680"
		},
		{
			"name": "Tag attribute",
			"scope": "entity.other.attribute-name",
			"foreground": "#ffb454"
		},
		{
			"name": "Library constant",
			"scope": "support.constant",
			"font_style": "italic",
			"foreground": "#f29668"
		},
		{
			"name": "Library class/type",
			"scope": "support.type, support.class, source.go storage.type",
			"foreground": "#39bae6"
		},
		{
			"name": "Decorators/annotation",
			"scope": "meta.decorator variable.other, meta.decorator punctuation.decorator, storage.type.annotation, variable.annotation, punctuation.definition.annotation",
			"foreground": "#e6b673"
		},
		{
			"name": "Invalid",
			"scope": "invalid",
			"foreground": "#d95757"
		},
		{
			"name": "diff.header",
			"scope": "meta.diff, meta.diff.header",
			"foreground": "#c594c5"
		},
		{
			"name": "Ruby class methods",
			"scope": "source.ruby variable.other.readwrite",
			"foreground": "#ffb454"
		},
		{
			"name": "CSS tag names",
			"scope": "source.css entity.name.tag, source.sass entity.name.tag, source.scss entity.name.tag, source.less entity.name.tag, source.stylus entity.name.tag",
			"foreground": "#59c2ff"
		},
		{
			"name": "CSS browser prefix",
			"scope": "source.css support.type, source.sass support.type, source.scss support.type, source.less support.type, source.stylus support.type",
			"foreground": "#acb6bf8c"
		},
		{
			"name": "CSS Properties",
			"scope": "support.type.property-name",
			"font_style": "normal",
			"foreground": "#39bae6"
		},
		{
			"name": "Search Results Nums",
			"scope": "constant.numeric.line-number.find-in-files - match",
			"foreground": "#acb6bf8c"
		},
		{
			"name": "Search Results Match Nums",
			"scope": "constant.numeric.line-number.match",
			"foreground": "#ff8f40"
		},
		{
			"name": "Search Results Lines",
			"scope": "entity.name.filename.find-in-files",
			"foreground": "#aad94c"
		},
		{
			"scope": "message.error",
			"foreground": "#d95757"
		},
		{
			"name": "Markup heading",
			"scope": "markup.heading, markup.heading entity.name",
			"font_style": "bold",
			"foreground": "#aad94c"
		},
		{
			"name": "Markup links",
			"scope": "markup.underline.link, string.other.link",
			"foreground": "#59c2ff"
		},
		{
			"name": "Markup Italic",
			"scope": "markup.italic",
			"font_style": "italic",
			"foreground": "#f07178"
		},
		{
			"name": "Markup Bold",
			"scope": "markup.bold",
			"font_style": "bold",
			"foreground": "#f07178"
		},
		{
			"name": "Markup Bold/italic",
			"scope": "markup.italic markup.bold, markup.bold markup.italic",
			"font_style": "bold italic"
		},
		{
			"name": "Markup Code",
			"scope": "markup.raw",
			"background": "#bfbdb605"
		},
		{
			"name": "Markup Code Inline",
			"scope": "markup.raw.inline",
			"background": "#bfbdb60f"
		},
		{
			"name": "Markdown Separator",
			"scope": "meta.separator",
			"font_style": "bold",
			"background": "#bfbdb60f",
			"foreground": "#acb6bf8c"
		},
		{
			"name": "Markup Blockquote",
			"scope": "markup.quote",
			"foreground": "#95e6cb",
			"font_style": "italic"
		},
		{
			"name": "Markup List Bullet",
			"scope": "markup.list punctuation.definition.list.begin",
			"foreground": "#ffb454"
		},
		{
			"name": "Markup added",
			"scope": "markup.inserted",
			"foreground": "#7fd962"
		},
		{
			"name": "Markup modified",
			"scope": "markup.changed",
			"foreground": "#73b8ff"
		},
		{
			"name": "Markup removed",
			"scope": "markup.deleted",
			"foreground": "#f26d78"
		},
		{
			"name": "Markup Strike",
			"scope": "markup.strike",
			"foreground": "#e6b673"
		},
		{
			"name": "Markup Table",
			"scope": "markup.table",
			"background": "#bfbdb60f",
			"foreground": "#39bae6"
		},
		{
			"name": "Markup Raw Inline",
			"scope": "text.html.markdown markup.inline.raw",
			"foreground": "#f29668"
		},
		{
			"name": "Markdown - Line Break",
			"scope": "text.html.markdown meta.dummy.line-break",
			"background": "#acb6bf8c",
			"foreground": "#acb6bf8c"
		},
		{
			"name": "Markdown - Raw Block Fenced",
			"scope": "punctuation.definition.markdown",
			"background": "#bfbdb6",
			"foreground": "#acb6bf8c"
		}
	]
}
