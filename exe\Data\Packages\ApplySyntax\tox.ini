[tox]
skipsdist=True
envlist =
    py35,py36,py37,py38,lint

[testenv]
deps=
    pytest
commands=
    py.test .

[testenv:documents]
deps=
    -rdocs/src/requirements.txt
commands=
    mkdocs build --clean --verbose --strict
    pyspelling

[testenv:lint]
deps=
    flake8
    flake8_docstrings
    pep8-naming
    flake8-mutable
    flake8-builtins
commands=
    flake8 "{toxinidir}"

[flake8]
ignore=D202,D203,D401,W504,E741
max-line-length=120
exclude=site/*.py,.tox/*
