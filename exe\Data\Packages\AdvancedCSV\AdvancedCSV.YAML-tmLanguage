# [PackageDev] target_format: plist, ext: tmLanguage
---
name: Advanced CSV
fileTypes: [csv, tsv]
scopeName: text.advanced_csv
uuid: 7ce133ea-e34b-47d9-a03c-341293337c88

patterns:
-   name: meta.quoted.advanced_csv
    begin: (\")
    end: (\")
    captures:
      '1': {name: string.quoted.double.advanced_csv}
    patterns:
      - include: '$self'

-   name: meta.range.advanced_csv
    begin: (\[([+-]?\d*)(\:)?([+-]?\d*)(\,)?([+-]?\d*)(\:)?([+-]?\d*)\])?\s*([<>v^])?\s*(=)
    beginCaptures:
      '1': {name: keyword.operator.advanced_csv} # range
      '2': {name: constant.numeric.formula.advanced_csv} # row_begin
      '4': {name: constant.numeric.formula.advanced_csv} # row_end
      '6': {name: constant.numeric.formula.advanced_csv} # col_begin
      '8': {name: constant.numeric.formula.advanced_csv} # col_end
      '9': {name: keyword.operator.advanced_csv} # direction
      '10': {name: keyword.operator.advanced_csv} # equals
    end: (?=(\")|$)
    patterns:
    - include: source.python

-   name: meta.number.advanced_csv
    match: (?<=^|,|\s|\")([0-9.eE+-]+)(?=$|,|\s|\")
    captures:
      '1': {name: constant.numeric.advanced_csv}

-   name: meta.nonnumber.advanced_csv
    match: (?<=^|,|\s|\")([^, \t\"]+)(?=$|,|\s|\")
    captures:
      '1': {name: storage.type.advanced_csv}

-   name: meta.delimiter.advanced_csv
    match: (\,)
    captures:
      '1': {name: keyword.operator.advanced_csv}

