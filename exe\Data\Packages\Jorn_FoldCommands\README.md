# Jorn_FoldCommands

A Sublime Text plugin for detecting and selecting markdown content based on heading fold levels.

## Purpose

This plugin is designed to help verify markdown heading detection and fold region calculation before implementing actual folding functionality. It allows you to:

1. **Detect markdown headings** - Both ATX (`# Heading`) and Setext (`Heading\n======`) styles
2. **Calculate fold regions** - Determine what content would be folded under each heading
3. **Select content by level** - Visually verify detection accuracy by selecting content
4. **Debug fold logic** - Output detailed information to the console

## Features

### Heading Detection
- **ATX Style**: `# H1`, `## H2`, `### H3`, etc. (up to H6)
- **Setext Style**: 
  - H1: `Heading` followed by `======`
  - H2: `Heading` followed by `------`

### Selection Commands
- **Select by specific level**: Select all content under headings of a specific level (1-6)
- **Select at or below level**: Select all content under headings at or below a specific level
- **Debug commands**: Show detected headings and fold regions in console

### Key Bindings (Markdown files only)
- `Ctrl+Alt+F, H` - Show all detected headings
- `Ctrl+Alt+F, R` - Show all fold regions
- `Ctrl+Alt+F, 1-6` - Select content at heading level 1-6
- `Ctrl+Alt+F, Shift+2` - Select content at level 2 and below
- `Ctrl+Alt+F, Shift+3` - Select content at level 3 and below

## Usage

1. **Open a markdown file** in Sublime Text
2. **Run detection commands** to see what headings are found:
   - Command Palette → "Jorn - Fold Commands: Show All Headings"
   - Check the console for detailed output
3. **Test selection** to verify accuracy:
   - Command Palette → "Jorn - Fold Commands: Select Level 2 Content"
   - This will select all content that would be folded under H2 headings
4. **Debug fold regions** if needed:
   - Command Palette → "Jorn - Fold Commands: Show All Fold Regions"

## Example

Given this markdown:
```markdown
# Main Title

Some intro content here.

## Section A

Content under section A.
More content here.

### Subsection A.1

Content under subsection A.1.

## Section B

Content under section B.
```

- **Level 1 selection** would select: intro content, Section A content, Subsection A.1 content, Section B content
- **Level 2 selection** would select: Section A content (including subsection), Section B content
- **Level 3 selection** would select: Subsection A.1 content only

## Settings

Available in `Jorn_FoldCommands.sublime-settings`:

- `debug_mode`: Enable verbose console output
- `default_fold_level`: Default level for operations
- `include_trailing_whitespace`: Include empty lines in fold regions
- `minimum_fold_lines`: Minimum lines required to create a fold region
- `support_atx_headings`: Enable ATX style heading detection
- `support_setext_headings`: Enable Setext style heading detection

## Next Steps

Once you've verified that the heading detection and fold region calculation work correctly:

1. Add actual folding commands that use `view.fold(region)`
2. Add unfolding commands that use `view.unfold(region)`
3. Add commands to fold/unfold specific levels
4. Consider adding automatic folding on file load
5. Add support for other document types if needed

## Development Notes

The plugin uses a `MarkdownFoldDetector` class that:
- Scans the document for heading patterns
- Calculates content regions under each heading
- Provides methods to filter by heading level
- Handles both ATX and Setext markdown heading styles

The selection commands are temporary - they help verify the detection logic before implementing actual folding functionality.
