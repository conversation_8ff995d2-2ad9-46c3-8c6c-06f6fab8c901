matrix:
- name: settings
  sources:
  - '**/*.sublime-settings'
  aspell:
    lang: en
  dictionary:
    wordlists:
    - docs/src/dictionary/en-custom.txt
    output: build/dictionary/settings.dic
  pipeline:
  - pyspelling.filters.cpp:
      prefix: 'st'
      group_comments: true
      line_comments: false
  - pyspelling.filters.context:
      context_visible_first: true
      escapes: '\\[\\`]'
      delimiters:
      # Ignore multiline content between fences (fences can have 3 or more back ticks)
      # ```
      # content
      # ```
      - open: '(?s)^(?P<open> *`{3,})$'
        close: '^(?P=open)$'
      # Ignore text between inline back ticks
      - open: '(?P<open>`+)'
        close: '(?P=open)'
  - pyspelling.filters.url:

- name: mkdocs
  sources:
  - site/**/*.html
  aspell:
    lang: en
  dictionary:
    wordlists:
    - docs/src/dictionary/en-custom.txt
    output: build/dictionary/mkdocs.dic
  pipeline:
  - pyspelling.filters.html:
      comments: false
      attributes:
      - title
      - alt
      ignores:
      - 'code, pre, a.magiclink, span.keys'
      - '.MathJax_Preview, .md-nav__link, .md-footer-custom-text, .md-source__repository, .headerlink, .md-icon'
      - '.md-social__link'
  - pyspelling.filters.url:

- name: markdown
  sources:
  - '*.md|messages/*.md'
  aspell:
    lang: en
  dictionary:
    wordlists:
    - docs/src/dictionary/en-custom.txt
    output: build/dictionary/markdown.dic
  pipeline:
  - pyspelling.filters.markdown:
  - pyspelling.filters.html:
      comments: false
      attributes:
      - title
      - alt
      ignores:
      - :is(code, pre)
  - pyspelling.filters.url:

- name: python
  sources:
  - '*.py|tests/**/*.py'
  aspell:
    lang: en
  dictionary:
    wordlists:
    - docs/src/dictionary/en-custom.txt
    output: build/dictionary/python.dic
  pipeline:
  - pyspelling.filters.python:
      group_comments: true
  - pyspelling.flow_control.wildcard:
      allow:
      - py-comment
  - pyspelling.filters.context:
      context_visible_first: true
      delimiters:
      # Ignore lint (noqa) and coverage (pragma) as well as shebang (#!)
      - open: '^(?: *(?:noqa\b|pragma: no cover)|!)'
        close: '$'
      # Ignore Python encoding string -*- encoding stuff -*-
      - open: '^ *-\*-'
        close: '-\*-$'
  - pyspelling.filters.context:
      context_visible_first: true
      escapes: '\\[\\`]'
      delimiters:
      # Ignore multiline content between fences (fences can have 3 or more back ticks)
      # ```
      # content
      # ```
      - open: '(?s)^(?P<open> *`{3,})$'
        close: '^(?P=open)$'
      # Ignore text between inline back ticks
      - open: '(?P<open>`+)'
        close: '(?P=open)'
  - pyspelling.filters.url:
