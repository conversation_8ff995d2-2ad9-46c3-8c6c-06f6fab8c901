site_name: ApplySyntax Documentation
site_url: https://facelessuser.github.io/ApplySyntax
repo_url: https://github.com/facelessuser/ApplySyntax
edit_uri: tree/master/docs/src/markdown
site_description: Syntax detector for Sublime Text.
copyright: |
  Copyright &copy; 2013 - 2021 <a href="https://github.com/facelessuser"  target="_blank" rel="noopener"><PERSON></a>

docs_dir: docs/src/markdown
theme:
  name: material
  custom_dir: docs/theme
  icon:
    logo: material/book-open-page-variant
  palette:
    scheme: dracula
    primary: deep purple
    accent: deep purple
  font:
    text: Roboto
    code: Roboto Mono
  features:
    - navigation.tabs
    - navigation.top

nav:
  - About ApplySyntax: index.md
  - Installation: installation.md
  - User Guide: usage.md
  - About:
    - License: about/license.md
    - Contributing &amp; Support: about/contributing.md
    - Changelog: about/changelog.md

markdown_extensions:
  - markdown.extensions.toc:
      slugify: !!python/name:pymdownx.slugs.uslugify
      permalink: ""
  - markdown.extensions.smarty:
      smart_quotes: false
  - pymdownx.betterem:
  - markdown.extensions.attr_list:
  - markdown.extensions.tables:
  - markdown.extensions.abbr:
  - markdown.extensions.md_in_html:
  - pymdownx.superfences:
  - pymdownx.highlight:
      extend_pygments_lang:
        - name: php-inline
          lang: php
          options:
            startinline: true
  - pymdownx.inlinehilite:
  - pymdownx.magiclink:
      repo_url_shortener: true
      repo_url_shorthand: true
      user: facelessuser
      repo: ApplySyntax
  - pymdownx.tilde:
  - pymdownx.caret:
  - pymdownx.smartsymbols:
  - pymdownx.emoji:
      emoji_index: !!python/name:materialx.emoji.twemoji
      emoji_generator: !!python/name:materialx.emoji.to_svg
  - pymdownx.escapeall:
      hardbreak: true
      nbsp: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.progressbar:
  - pymdownx.striphtml:
  - pymdownx.snippets:
      base_path:
      - docs/src/markdown/.snippets
      - CHANGES.md
  - pymdownx.keys:
      separator: "\uff0b"
  - pymdownx.saneheaders:
  - pymdownx.blocks.admonition:
      types:
      - new
      - settings
      - note
      - abstract
      - info
      - tip
      - success
      - question
      - warning
      - failure
      - danger
      - bug
      - example
      - quote
  - pymdownx.blocks.details:
  - pymdownx.blocks.html:
  - pymdownx.blocks.definition:
  - pymdownx.blocks.tab:
      alternate_style: True

extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/facelessuser
    - icon: fontawesome/brands/discord
      link: https://discord.gg/TWs8Tgr

plugins:
  - search
  - git-revision-date-localized
  - mkdocs_pymdownx_material_extras
  - minify:
      minify_html: true
