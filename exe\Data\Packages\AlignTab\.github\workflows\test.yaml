name: test

on: [push, pull_request]

jobs:
  run-tests:
    strategy:
      fail-fast: false
      matrix:
        st-version: [3, 4]
    runs-on: "ubuntu-latest"
    steps:
      - uses: actions/checkout@v2
      - uses: SublimeText/UnitTesting/actions/setup@master
        with:
          sublime-text-version: ${{ matrix.st-version }}
      - uses: SublimeText/UnitTesting/actions/run-tests@master
        with:
          coverage: true
          codecov-upload: true
