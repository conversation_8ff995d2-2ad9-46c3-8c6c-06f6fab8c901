<snippet>
    <!-- [INSERTED CODE] -->
    <content><![CDATA[
:: Set current directory to the location of this batch file (or 'input-arg-1')
IF "%~1" == "" (CD /D "%~dp0") ELSE (CD /D "%~1")
]]></content>
    <!-- Optional: Tab trigger to activate the snippet -->
    <tabTrigger>cd</tabTrigger>
    <!-- Optional: Scope the tab trigger will be active in -->
    <scope>source.dosbatch</scope>
    <!-- Optional: Description to show in the menu -->
    <description>(Custom) Set current directory</description>
</snippet>
