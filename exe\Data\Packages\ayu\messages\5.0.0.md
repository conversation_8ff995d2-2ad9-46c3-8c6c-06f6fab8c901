**ayu theme for Sublime Text**
https://github.com/dempfi/ayu/releases

********************************************************************************
ayu from version 3.0.0 supports customization via A File Icon package
(https://github.com/ihodev/a-file-icon). Please install it for better expereince.
********************************************************************************

# Version 5.0.0

- 🎉 Refreshed UI theme:
  - Improved editor/ui contrast when `ui_separators` options is on;
  - Resized status bar;
  - Support for Sublime's git indicators;
  - Added missing accents on hover;
  and other minor fixes

- 🎉 Refreshed syntax theme
  - Fixed inconsistencies with Java, C, Haskell, TS, JS, GO, Python and Ruby;
  - Updated markdown colour scheme
  and other minor fixes

- Internal:
  - New streamlined build system for streamlined contrinution and forks;
  - Official `ayu-colors` npm package is now used as the source of the colours;

Breaking changes:
*******************************************************
- Removed UI font settings (`ui_font_size_small`, `ui_font_source_code_pro`, `ui_font_roboto_mono`)
