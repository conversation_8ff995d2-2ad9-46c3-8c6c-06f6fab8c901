<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>comment</key>
    <string>https://scotch.io</string>
    <key>name</key>
    <string>10% Too Dull for My Tastes</string>
    <key>settings</key>
    <array>
        <dict>
            <key>settings</key>
            <dict>
                <key>background</key>
                <string>#111214</string>
                <key>caret</key>
                <string>#ffffff</string>
                <key>foreground</key>
                <string>#ffffff</string>
                <key>invisibles</key>
                <string>#434D5B</string>
                <key>lineHighlight</key>
                <string>#343c48</string>
                <key>selection</key>
                <string>#434D5B</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Comment</string>
            <key>scope</key>
            <string>comment, punctuation.definition.comment, string.quoted.double.block.python</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string>italic</string>
                <key>foreground</key>
                <string>#535e6f</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Foreground</string>
            <key>scope</key>
            <string>keyword.operator.class, source.php.embedded.line, meta.method punctuation.definition, meta.method punctuation.separator</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#CED1CF</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Variable, String Link, Regular Expression, Tag Name</string>
            <key>scope</key>
            <string>variable, support.other.variable, variable.parameter, string.other.link, string.regexp, declaration.tag, meta.expression.body.function, variable.parameter, string.unquoted.label</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#fffbd4</string>
            </dict>
        </dict>







        <dict>
            <key>name</key>
            <string>Number, Constant, Function Argument, Tag Attribute, Embedded</string>
            <key>scope</key>
            <string>constant.numeric, constant.language, constant.other, support.constant, variable.other.constant, keyword.other.unit, meta.property-value, punctuation.section.embedded</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#e5d4ff</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Class, Support</string>
            <key>scope</key>
            <string>entity.name.class, entity.name.type.class, entity.name.type.instance, entity.name.instance, meta.instance.constructor, meta.property.class, variable.other.class, class.name, support.type, support.class, storage.type</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#fefffd</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>String, Symbols, Inherited Class, Markup Heading</string>
            <key>scope</key>
            <string>string, constant.other.symbol, entity.other.inherited-class</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#bdc1c7</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Operator, Misc</string>
            <key>scope</key>
            <string>keyword.operator, keyword.control, entity.other.attribute-name, constant.other.color, constant.character</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ffb5a0</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Function, Special Method, Block Level</string>
            <key>scope</key>
            <string>entity.name.function, support.function, keyword.other.special-method, entity.name.method, meta.accessor, meta.block-level, function.name</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#9ccdff</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Keyword, Storage</string>
            <key>scope</key>
            <string>entity.name.tag.css, meta.tag, entity.name.tag</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#ff8a8a</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Keyword, Storage</string>
            <key>scope</key>
            <string>keyword, storage, storage.type</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string>italic</string>
                <key>foreground</key>
                <string>#ff8a8a</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Java Methods</string>
            <key>scope</key>
            <string>meta.method.body.java, meta.method.return-type.java</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#CED1CF</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Java Strings</string>
            <key>scope</key>
            <string>punctuation.definition.string.begin.java, punctuation.definition.string.end.java</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#bdc1c7</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Java Classes, Storage Types</string>
            <key>scope</key>
            <string>storage.type.java</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#fefffd</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>TypeScript Functions</string>
            <key>scope</key>
            <string>meta.expression.body.function</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#9ccdff</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>TypeScript Class</string>
            <key>scope</key>
            <string>meta.expression.body.class.ts, meta.class.ts</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#fefffd</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>TypeScript Variables</string>
            <key>scope</key>
            <string>meta.toc-list.class.member.ts</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#fffbd4</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>TypeScript Strings</string>
            <key>scope</key>
            <string>punctuation.definition.string.ts</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#bdc1c7</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>TypeScript Misc., Braces, Periods, Delimiter etc.</string>
            <key>scope</key>
            <string>meta.brace.curly.ts, meta.brace.square.ts, meta.brace.round.ts, meta.delimiter.ts, punctuation.definition.parameters.ts, punctuation.terminator.statement.ts, punctuation.definition.parameters.begin.ts, punctuation.definition.parameters.end.ts, meta.delimiter.method.period.ts, meta.delimiter.object.comma.ts, keyword.operator.ts</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#ffffff</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>JSON Key</string>
            <key>scope</key>
            <string>string.quoted.double.json, meta.structure.dictionary.json string.quoted.double.json, meta.structure.dictionary.json meta.structure.dictionary.json string.quoted.double.json, meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json string.quoted.double.json, meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json string.quoted.double.json, meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json string.quoted.double.json, meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json string.quoted.double.json, meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json string.quoted.double.json, meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json meta.structure.dictionary.json string.quoted.double.json</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#fffbd4</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>JSON String</string>
            <key>scope</key>
            <string>meta.structure.dictionary.value.json string.quoted.double.json, meta.structure.dictionary.value.json meta.structure.dictionary.value.json string.quoted.double.json, meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json string.quoted.double.json, meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json string.quoted.double.json, meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json string.quoted.double.json, meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json string.quoted.double.json, meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json string.quoted.double.json, meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json string.quoted.double.json, meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json string.quoted.double.json, meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json meta.structure.dictionary.value.json string.quoted.double.json</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#bdc1c7</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>JavaScript Strings</string>
            <key>scope</key>
            <string>meta.parameter.optional punctuation.definition.string.begin, meta.parameter.optional punctuation.definition.string.end</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#bdc1c7</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Markdown Titles</string>
            <key>scope</key>
            <string>markup.heading.markdown, markup.heading</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#fefffd</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Markdown lists</string>
            <key>scope</key>
            <string>markup.list.unnumbered.markdown, markup.list.numbered.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ffffff</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Markdown bold/italic</string>
            <key>scope</key>
            <string>markup.bold.markdown, markup.italic.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#FFFFFF</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Markdown italic</string>
            <key>scope</key>
            <string>markup.italic.markdown</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string>italic</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Markdown bold</string>
            <key>scope</key>
            <string>markup.bold.markdown</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string>bold</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Markdown Raw/Pre</string>
            <key>scope</key>
            <string>markup.raw.inline.markdown, markup.raw.block.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#fffbd4</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Markdown String Link</string>
            <key>scope</key>
            <string>string.other.link.title.markdown, string.other.link.description.title.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#9ccdff</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Markdown link</string>
            <key>scope</key>
            <string>markup.underline.link.markdown, markup.underline.link.image.markdown, meta.image.inline.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#e5d4ff</string>
                <key>fontStyle</key>
                <string>italic</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Markdown quote</string>
            <key>scope</key>
            <string>markup.quote.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#bdc1c7</string>
            </dict>
        </dict>
        <dict>
            <key>scope</key>
            <string>constant.numeric.line-number.find-in-files - match</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#e5d4ffA0</string>
            </dict>
        </dict>
        <dict>
            <key>scope</key>
            <string>entity.name.filename.find-in-files</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#fefffd</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Invalid</string>
            <key>scope</key>
            <string>invalid</string>
            <key>settings</key>
            <dict>
                <key>background</key>
                <string>#ff7e7e</string>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#ffffff</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Separator</string>
            <key>scope</key>
            <string>meta.separator</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ffb5a0</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Deprecated</string>
            <key>scope</key>
            <string>invalid.deprecated</string>
            <key>settings</key>
            <dict>
                <key>background</key>
                <string>#CED2CF</string>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string></string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Diff foreground</string>
            <key>scope</key>
            <string>markup.inserted.diff, markup.deleted.diff, meta.diff.header.to-file, meta.diff.header.from-file</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ffffff</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Diff insertion</string>
            <key>scope</key>
            <string>markup.inserted.diff, meta.diff.header.to-file</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#A6E22E</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Diff deletion</string>
            <key>scope</key>
            <string>markup.deleted.diff, meta.diff.header.from-file</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ff7e7e</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Diff header</string>
            <key>scope</key>
            <string>meta.diff.header.from-file, meta.diff.header.to-file</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ffffff</string>
                <key>background</key>
                <string>#4271ae</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Diff range</string>
            <key>scope</key>
            <string>meta.diff.range</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string>italic</string>
                <key>foreground</key>
                <string>#ffb5a0</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>diff.deleted</string>
            <key>scope</key>
            <string>markup.deleted</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#F92672</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>diff.inserted</string>
            <key>scope</key>
            <string>markup.inserted</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#A6E22E</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>diff.changed</string>
            <key>scope</key>
            <string>markup.changed</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#967EFB</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>SublimeLinter Error</string>
            <key>scope</key>
            <string>sublimelinter.mark.error</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ff7e7e</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>SublimeLinter Gutter Mark</string>
            <key>scope</key>
            <string>sublimelinter.gutter-mark</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ffffff</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>SublimeLinter Warning</string>
            <key>scope</key>
            <string>sublimelinter.mark.warning</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#FACB68</string>
            </dict>
        </dict>
    </array>
    <key>uuid</key>
    <string>N96993EB-1A60-4617-92F3-D24D4F13DB69</string>
    <key>colorSpaceName</key>
    <string>sRGB</string>
    <key>semanticClass</key>
    <string>theme.dark.10PercentTooDullForMyTastes</string>
</dict>
</plist>