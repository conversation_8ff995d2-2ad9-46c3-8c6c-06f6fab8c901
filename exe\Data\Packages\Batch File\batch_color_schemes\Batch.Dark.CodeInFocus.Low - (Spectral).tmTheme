<?xml version="1.0" encoding="UTF-8"?>
<plist version="1.0">
<dict>
	<key>comment</key>
	<string>Enjoy this Theme</string>
	<key>author</key>
	<string>Battlelore</string>
	<key>name</key>
	<string>Spectral</string>
	<key>settings</key>
	<array>
		<dict>
			<key>settings</key>
			<dict>
				<key>background</key>
				<string>#090909</string>
				<key>caret</key>
				<string>#ed0909</string>
				<key>foreground</key>
				<string>#d5d8e9</string>
				<key>selection</key>
				<string>#4B1010</string>
				<key>lineHighlight</key>
				<string>hsla(190, 100%, 15%, 0.35)</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Comment</string>
			<key>scope</key>
			<string>comment</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
        </string>
				<key>foreground</key>
				<string>#555</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>String</string>
			<key>scope</key>
			<string>string</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#35d2ab</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>String Embedded Source</string>
			<key>scope</key>
			<string>string source</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#A9B7C6</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Number</string>
			<key>scope</key>
			<string>constant.numeric</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#FFD442</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Built-in constant</string>
			<key>scope</key>
			<string>constant.language</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#A5C25C</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>User-defined constant</string>
			<key>scope</key>
			<string>constant.character, constant.other</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#A5C25C</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Keyword</string>
			<key>scope</key>
			<string>keyword</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#CC7832</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Support</string>
			<key>scope</key>
			<string>support</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#A9B7C6</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Storage</string>
			<key>scope</key>
			<string>storage</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#CC7832</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Class name</string>
			<key>scope</key>
			<string>entity.name.class, entity.name.type.class, entity.name.type.module</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#CC7832</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Inherited class</string>
			<key>scope</key>
			<string>entity.other.inherited-class</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
     bold</string>
				<key>foreground</key>
				<string>#C7444A</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Function name</string>
			<key>scope</key>
			<string>entity.name.function</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
     bold</string>
				<key>foreground</key>
				<string>#CC7832</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Function argument</string>
			<key>scope</key>
			<string>variable.parameter</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#6089B4</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Tag name</string>
			<key>scope</key>
			<string>entity.name.tag</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Tag attribute</string>
			<key>scope</key>
			<string>entity.other.attribute-name</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Library function</string>
			<key>scope</key>
			<string>support.function</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Class Variable</string>
			<key>scope</key>
			<string>variable.other, variable.js, punctuation.separator.variable</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#6089B4</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Language Constant</string>
			<key>scope</key>
			<string>constant.language</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#AA0000</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Invalid</string>
			<key>scope</key>
			<string>invalid</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#FF0B00</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Meta Brace</string>
			<key>scope</key>
			<string>punctuation.section.embedded -(source string source punctuation.section.embedded), meta.brace.erb.html</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#9aa83a</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Normal Variable</string>
			<key>scope</key>
			<string>variable.other.php, variable.other.normal</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#6089b4</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Function Call</string>
			<key>scope</key>
			<string>meta.function-call</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#6089b4</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Function Object</string>
			<key>scope</key>
			<string>meta.function-call.object</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Function Call Variable</string>
			<key>scope</key>
			<string>variable.other.property</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Keyword Control</string>
			<key>scope</key>
			<string>keyword.control</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Tag</string>
			<key>scope</key>
			<string>meta.tag</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Tag Name</string>
			<key>scope</key>
			<string>entity.name.tag</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#75ACCF</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Doctype</string>
			<key>scope</key>
			<string>meta.doctype, meta.tag.sgml-declaration.doctype, meta.tag.sgml.doctype</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#c0c0c0</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Tag Inline Source</string>
			<key>scope</key>
			<string>meta.tag.inline source, text.html.php.source</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#9aa83a</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Tag Other</string>
			<key>scope</key>
			<string>meta.tag.other, entity.name.tag.style, entity.name.tag.script, meta.tag.block.script, source.js.embedded punctuation.definition.tag.html, source.css.embedded punctuation.definition.tag.html</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Tag Attribute</string>
			<key>scope</key>
			<string>entity.other.attribute-name, meta.tag punctuation.definition.string</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#316494</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Tag Value</string>
			<key>scope</key>
			<string>meta.tag string -source -punctuation, text source text meta.tag string -punctuation</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#6089B4</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Meta Brace</string>
			<key>scope</key>
			<string>punctuation.section.embedded -(source string source punctuation.section.embedded), meta.brace.erb.html</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>HTML ID</string>
			<key>scope</key>
			<string>meta.toc-list.id</string>
			<key>settings</key>
			<dict>
				<key>foreground</key>
				<string>#9aa83a</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>HTML String</string>
			<key>scope</key>
			<string>string.quoted.double.html, punctuation.definition.string.begin.html, punctuation.definition.string.end.html</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#9aa83a</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>HTML Tags</string>
			<key>scope</key>
			<string>punctuation.definition.tag.html, punctuation.definition.tag.begin, punctuation.definition.tag.end</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#6089B4</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>CSS ID</string>
			<key>scope</key>
			<string>meta.selector.css entity.other.attribute-name.id</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#316494</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>CSS Property Name</string>
			<key>scope</key>
			<string>support.type.property-name.css</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#7550ac</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>CSS Property Value</string>
			<key>scope</key>
			<string>meta.property-group support.constant.property-value.css, meta.property-value support.constant.property-value.css</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#C7444A</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>JavaScript Variable</string>
			<key>scope</key>
			<string>variable.language.js</string>
			<key>settings</key>
			<dict>
				<key>foreground</key>
				<string>#CC555A</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>PHP Function Call</string>
			<key>scope</key>
			<string>meta.function-call.object.php</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>PHP Single Quote HMTL Fix</string>
			<key>scope</key>
			<string>punctuation.definition.string.end.php, punctuation.definition.string.begin.php</string>
			<key>settings</key>
			<dict>
				<key>foreground</key>
				<string>#9aa83a</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>PHP Parenthesis HMTL Fix</string>
			<key>scope</key>
			<string>source.php.embedded.line.html</string>
			<key>settings</key>
			<dict>
				<key>foreground</key>
				<string>#676867</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>PHP Punctuation Embedded</string>
			<key>scope</key>
			<string>punctuation.section.embedded.begin.php, punctuation.section.embedded.end.php</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#cb602d</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Ruby Symbol</string>
			<key>scope</key>
			<string>constant.other.symbol.ruby</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#9aa83a</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Ruby Variable</string>
			<key>scope</key>
			<string>variable.language.ruby</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D0B344</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Ruby Special Method</string>
			<key>scope</key>
			<string>keyword.other.special-method.ruby</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#D9B700</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>Ruby Embedded Source</string>
			<key>scope</key>
			<string>source.ruby.embedded.source</string>
			<key>settings</key>
			<dict>
				<key>foreground</key>
				<string>#cb602d</string>
			</dict>
		</dict>
		<dict>
			<key>name</key>
			<string>SQL</string>
			<key>scope</key>
			<string>keyword.other.DML.sql</string>
			<key>settings</key>
			<dict>
				<key>fontStyle</key>
				<string>
    </string>
				<key>foreground</key>
				<string>#e8bc7b</string>
			</dict>
		</dict>
	</array>
</dict>
</plist>
