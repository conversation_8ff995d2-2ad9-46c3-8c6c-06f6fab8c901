%YAML 1.2
---
name: build2 buildfile
file_extensions:
  - bootstrap.build
  - bootstrap.build2
  - config.build
  - config.build2
  - export.build
  - export.build2
  - root.build
  - root.build2
  - src-root.build
  - src-root.build2
  - out-root.build
  - out-root.build2
  - pre-bdep-sync.build
  - pre-bdep-sync.build2
  - buildfile
  - build2file
scope: source.build2.buildfile
contexts:
  main:
    - include: multi_line_comments
    - include: line_comments
    - include: directives
    - include: diagnostic_directives
    - include: assert_directives
    - include: dump_directives
    - include: import_directives
    - include: define_declarations
    - include: config_directives
    - include: conditions
    - include: loops
    - include: dependency_declarations
    - include: adhoc_recipes
    - include: variable_assignments
    - include: function_calls
    - include: variable_expansions
    - include: eval_contexts
    - include: directories
    - include: double_quotes
    - include: single_quotes

  bare_line_mode:
    - match: '\\\\'
      scope: constant.character.escape.build2.buildfile
    - match: '\\\n'
      scope: constant.character.escape.build2.buildfile
    - match: '(\\ |\\\(|\\\)|\\\{|\\\}|\\\[|\\\]|\\\@|\\\#|\\\$|\\")'
      scope: constant.character.escape.build2.buildfile
    - match: '\n'
      pop: true
    - include: line_comments
    - include: targets
    - include: function_calls
    - include: variable_expansions
    - include: eval_contexts
    - include: constants

  line_mode:
    - include: bare_line_mode
    - include: double_quotes
    - include: single_quotes

  constants:
    - match: '\b(true|false|null)\b'
      scope: constant.language.build2.buildfile

  line_comments:
    - match: '#'
      scope: punctuation.definition.comment.build2.buildfile
      push: line_comment
  line_comment:
    - meta_scope: comment.line.build2.buildfile
    - match: $
      pop: true

  multi_line_comments:
    - match: '^[\s]*(#\\)[\s]*$'
      captures:
        1: punctuation.definition.comment.begin.build2.buildfile.multi_line_comments
      push: multi_line_comment
  multi_line_comment:
    - meta_scope: comment.block.build2.buildfile.multi_line_comment
    - match: '^[\s]*(#\\)[\s]*$'
      captures:
        1: punctuation.definition.comment.end.build2.buildfile.multi_line_comment
      pop: true

  single_quotes:
    - match: "'"
      scope: punctuation.definition.string.begin.build2.buildfile
      push: single_quote
  single_quote:
    - meta_scope: string.quoted.single.build2.buildfile
    - match: "'"
      scope: punctuation.definition.string.end.build2.buildfile
      pop: true

  double_quotes:
    - match: '"'
      scope: punctuation.definition.string.begin.build2.buildfile.double_quotes
      push: double_quote
  double_quote:
    - meta_scope: string.quoted.double.build2.buildfile
    - include: bare_line_mode
    - match: '\\"'
      scope: constant.character.escape.build2.buildfile.double_quote
    - match: '"'
      scope: punctuation.definition.string.end.build2.buildfile.double_quote
      pop: true

  blocks:
    - match: '^[\s]*(\{)[\s]*$'
      captures:
        1: punctuation.section.block.begin.build2.buildfile
      push: block
  block:
    - meta_scope: meta.block.build2.buildfile
    - include: main
    - match: '^[\s]*(\})[\s]*$'
      pop: true

  conditions:
    - match: '\b(if|elif|else|if!|elif!)\b'
      scope: keyword.control.build2.buildfile.condition
      push: [condition_scopes, condition]
  condition:
    - include: line_mode
  condition_scopes:
    - match: '^[\s]*(\{)[\s]*$'
      captures:
        1: punctuation.section.block.begin.build2.buildfile.condition_scope
      set: condition_scope
    - match: '(?=.)'
      pop: true
  condition_scope:
    - meta_scope: meta.block.build2.buildfile.condition_scope
    - include: main
    - match: '^[\s]*(\})[\s]*$'
      captures:
        1: punctuation.section.block.end.build2.buildfile.condition_scope
      pop: true

  loops:
    - match: '\bfor\b'
      scope: keyword.control.build2.buildfile.for
      push: [loop_scopes, loop]
  loop:
    - include: line_mode
    - include: name_generations
  loop_scopes:
    - match: '^[\s]*(\{)[\s]*$'
      captures:
        1: punctuation.section.block.begin.build2.buildfile.loop_scope
      set: loop_scope
    - match: '(?=.)'
      pop: true
  loop_scope:
    - meta_scope: meta.block.build2.buildfile.loop_scope
    - include: main
    - match: '^[\s]*(\})[\s]*$'
      captures:
        1: punctuation.section.block.end.build2.buildfile.loop_scope
      pop: true

  directories:
    - match: '.(/)[\s]*\n'
      captures:
        1: constant.character.build2.buildfile.directories
      push: directory_scopes
  directory_scopes:
    - match: '^[\s]*(\{)[\s]*$'
      captures:
        1: punctuation.section.block.begin.build2.buildfile.directory_scopes
      set: directory_scope
  directory_scope:
    - meta_scope: meta.block.build2.buildfile.directory_scope
    - include: main
    - match: '^[\s]*(\})[\s]*$'
      pop: true

  single_type_targets:
    - match: '([a-z]+)(\{)'
      captures:
        1: entity.name.type.build2.buildfile.target.type
        2: punctuation.section.braces.begin.build2.buildfile.target.name
      push: target_name_pattern

  multi_type_targets:
    - match: '(\{)(([a-z]+\s*)+)(\})(\{)'
      captures:
        1: punctuation.section.braces.begin.build2.buildfile.target.type
        2: entity.name.type.build2.buildfile.target.type
        4: punctuation.section.braces.end.build2.buildfile.target.type
        5: punctuation.section.braces.begin.build2.buildfile.target.name
      push: target_name_pattern

  no_type_targets:
    - match: '\{'
      scope: punctuation.section.braces.begin.build2.buildfile.target.name
      push: target_name_pattern

  target_name_pattern:
    - meta_scope: meta.braces.build2.buildfile.target.name
    - match: '(\*\*\*|\*\*|\*|\.|\?|\[|\]|\-|\+)'
      scope: keyword.operator.build2.buildfile
    - match: '\}'
      scope: punctuation.section.braces.end.build2.buildfile
      pop: true
    - include: line_mode

  targets:
    - match: '\b(manifest|testscript)\b'
      scope: entity.name.type.build2.buildfile
    - include: single_type_targets
    - include: multi_type_targets
    - include: no_type_targets

  dependency_declarations:
    - include: dependency_declaration_directories
    - include: dependency_declaration_single_type_targets
    - include: dependency_declaration_target_groups
  dependency_declaration:
    - match: ':'
      scope: keyword.operator.build2.buildfile
    - include: line_mode

  dependency_declaration_directories:
    - match: '(?!\s)(/)[\s]*(:)'
      captures:
        1: constant.character.build2.buildfile
        2: keyword.operator.build2.buildfile
      push: dependency_declaration

  dependency_declaration_single_type_targets:
    - match: '([a-z]+)(\{)'
      captures:
        1: entity.name.type.build2.buildfile.target.type
        2: punctuation.section.braces.begin.build2.buildfile.target.name
      push: [dependency_declaration, target_name_pattern]

  dependency_declaration_target_groups:
    - match: '<'
      scope: punctuation.section.group.begin.build2.buildfile.target.group
      push: [dependency_declaration, dependency_declaration_target_group]
  dependency_declaration_target_group:
    - meta_scope: meta.group.build2.buildfile.target.group
    - include: targets
    - match: '>'
      pop: true
    - include: line_mode

  # type_generation:
  #   - meta_scope: meta.braces.build2.buildfile.type_generation
  #   - include: line_mode
  #   - match: '(\*\*\*|\*\*|\*|\.|\?)'
  #     scope: keyword.operator.build2.buildfile
  #     set: target_name
  #   - match: '(?!\s)(/)'
  #     set: target_name
  #   - match: '([a-z]+)'
  #     scope: entity.name.type.build2.buildfile
  #   - match: '(\})(\{)'
  #     captures:
  #       1: punctuation.section.braces.end.build2.buildfile.type_generation
  #       2: punctuation.section.braces.begin.build2.buildfile.target_name
  #     set: target_name

  # dependency_type_generation:
  #   - meta_scope: meta.braces.build2.buildfile.dependency_type_generation
  #   - include: line_mode
  #   - match: '(\*\*\*|\*\*|\*|\.|\?)'
  #     scope: keyword.operator.build2.buildfile
  #     set: name_pattern
  #   - match: '(?!\s)(/)'
  #     set: name_pattern
  #   - match: '([a-z]*)'
  #     scope: entity.name.type.build2.buildfile
  #   - match: '\}\{'
  #     scope: punctuation.section.braces.end.build2.buildfile
  #     set: name_pattern

  # target_name:
  #   - meta_scope: meta.braces.build2.buildfile.target_name
  #   - include: line_mode
  #   - match: '(\*\*\*|\*\*|\*|\.|\?)'
  #     scope: keyword.operator.build2.buildfile
  #   - match: '(\})[\s]*(:)'
  #     captures:
  #       1: punctuation.section.braces.end.build2.buildfile
  #       2: keyword.operator.build2.buildfile
  #     set: dependencies

  # name_pattern:
  #   - meta_scope: meta.braces.build2.buildfile.name_pattern
  #   - include: line_mode
  #   - match: '(\*\*\*|\*\*|\*|\.\.\.|\.\.|\.|\?|\+|\-)'
  #     scope: keyword.operator.build2.buildfile
  #   - match: '\}'
  #     scope: punctuation.section.braces.end.build2.buildfile
  #     pop: true

  # targets:
  #   - meta_scope: meta.dependency.build2.buildfile
  #   - match: '([a-z]+)(\{)'
  #     captures:
  #       1: entity.name.type.build2.buildfile
  #       2: punctuation.section.braces.begin.build2.buildfile
  #     push: name_pattern
  #   - match: '\b(manifest|testscript)\b'
  #     captures:
  #       1: entity.name.type.build2.buildfile
  #   - match: '\{'
  #     scope: punctuation.section.braces.begin.build2.buildfile
  #     push: dependency_type_generation

  # dependencies:
  #   - meta_scope: meta.dependency.build2.buildfile
  #   - include: line_mode
  #   - match: '(?=(\+=|=\+|=))'
  #     scope: keyword.operator.assignment.build2.buildfile
  #     pop: true
  #   - match: ':'
  #     scope: keyword.operator
  #   - match: '([a-z]+)(\{)'
  #     captures:
  #       1: entity.name.type.build2.buildfile
  #       2: punctuation.section.braces.begin.build2.buildfile
  #     push: name_pattern
  #   - match: '\b(manifest|testscript)\b'
  #     captures:
  #       1: entity.name.type.build2.buildfile
  #   - match: '\{'
  #     scope: punctuation.section.braces.begin.build2.buildfile
  #     push: dependency_type_generation

  # target_scopes:
  #   - match: '^[\s]*(\{)[\s]*$'
  #     captures:
  #       1: punctuation.section.block.begin.build2.buildfile.target_scope
  #     set: target_scope
  #   - match: '\n'
  #     pop: true
  #   - match: '(?=.)'
  #     pop: true
  # target_scope:
  #   - meta_scope: meta.block.build2.buildfile.target_scope
  #   - include: main
  #   - match: '^[\s]*(\})[\s]*$'
  #     captures:
  #       1: punctuation.section.block.end.build2.buildfile.target_scope
  #     pop: true

  directives:
    - match: '\b(include|using|export)\b'
      scope: keyword.control.import.build2.buildfile
      push: directive
  directive:
    - meta_scope: meta.directive.build2.buildfile
    - include: line_mode

  diagnostic_directives:
    - match: '\b(info|text|warn|fail|print)\b'
      scope: keyword.control.import.build2.buildfile
      push: diagnostic_directive
  diagnostic_directive:
    - meta_scope: string.quoted.double.build2.buildfile
    - include: line_mode

  assert_directives:
    - match: '\b(assert)\b'
      scope: keyword.control.import.build2.buildfile
      push: assert_directive
  assert_directive:
    - meta_scope: meta.directive.build2.buildfile
    - include: line_mode

  dump_directives:
    - match: '\b(dump)\b'
      scope: keyword.control.import.build2.buildfile
      push: dump_directive
  dump_directive:
    - meta_scope: meta.directive.build2.buildfile
    - include: line_mode

  import_directives:
    - match: '\b(import)\s'
      scope: keyword.control.import.build2.buildfile
      push: import_directive
    - match: '\b(import)(!)\s'
      captures:
        1: keyword.control.import.build2.buildfile
        2: constant.character.build2.buildfile.import
      push: import_directive
  import_directive:
    - meta_scope: meta.directive.build2.buildfile
    - match: '\b([a-zA-Z][a-zA-Z0-9_.]*)\b\s*(\+=|=\+|=)'
      captures:
        1: variable.other.build2.buildfile
        2: keyword.operator.assignment.build2.buildfile
      set: import_assignment
    - include: line_mode
  import_assignment:
    - meta_scope: meta.variable_assignment.build2.buildfile
    # Package Importation Match
    - match: '([a-zA-Z][a-zA-Z0-9_.\-]*)(\%)([a-z]+)(\{)([a-zA-Z][a-zA-Z0-9_.\-]*)(\})'
      captures:
        1: meta.braces.build2.buildfile.target_name
        2: constant.character.build2.buildfile.package.targets
        3: entity.name.type.build2.buildfile.target.type
        4: punctuation.section.braces.begin.build2.buildfile
        5: meta.braces.build2.buildfile.target_name
        6: punctuation.section.braces.end.build2.buildfile
    - include: line_mode

  define_declarations:
    - meta_scope: meta.directive.build2.buildfile.define
    - match: '\b(define)\b'
      scope: keyword.declaration.build2.buildfile
      push: define_declaration
  define_declaration:
    - match: '\b([a-z]+)\b\s*(\:)\s*\b([a-z]+)\b'
      captures:
        1: entity.name.type.build2.buildfile.target.type
        2: punctuation.definition.build2.buildfile
        3: entity.name.type.build2.buildfile.target.type
    - include: line_mode

  config_directives:
    - meta_scope: meta.directive.build2.buildfile.define.config
    - match: '\b(config)\s'
      scope: keyword.declaration.build2.buildfile.config
      push: config_directive_attribute_list_optional
  config_directive_attribute_list_optional:
    - match: '\['
      scope: punctuation.section.brackets.begin.build2.buildfile.config.attributes
      set: config_directive_attribute_list
    - include: config_directive_variable
  config_directive_attribute_list:
    - meta_scope: meta.brackets.build2.buildfile.config.attributes
    - match: '\b(bool|uint64|uint64s|string|strings|path|paths|dir_path|dir_paths|name|names|name_pair|project_name|target_triplet)\b'
      scope: storage.type.build2.buildfile.config
    - match: '\]'
      scope: punctuation.section.brackets.end.build2.buildfile.config.attributes
      set: config_directive_variable
    - include: line_mode
  config_directive_variable:
    - match: '\b(config(.[a-zA-Z][a-zA-Z0-9_]*)+)\b'
      scope: variable.other.build2.buildfile.config
      set: config_directive_default_assignment_optional
    - include: line_mode
  config_directive_default_assignment_optional:
    - match: '\?\='
      scope: keyword.operator.assignment.build2.buildfile.config
    - include: line_mode

  variable_assignments:
    - meta_scope: test
    - match: '\b(project|extension|backlink)\b'
      scope: variable.language.build2.buildfile
    - match: '\b(config.)?(cc|cxx|c)(.export)?(.(poptions|coptions|loptions|aoptions|libs))?\b'
      scope: variable.language.build2.buildfile
    - match: '\b([a-zA-Z][a-zA-Z0-9_.]*)\b'
      scope: variable.other.build2.buildfile
    - match: '(\+=|=\+|=)'
      scope: keyword.operator.assignment.build2.buildfile
      push: variable_assignment
  variable_assignment:
    - meta_scope: meta.variable_assignment.build2.buildfile
    - include: line_mode
    - include: name_generations
    - include: value_attributes

  variable_expansions:
    - match: '\$[a-zA-Z][a-zA-Z0-9_.]*\b'
      scope: variable.parameter.build2.buildfile
    - match: '\$\([a-zA-Z][a-zA-Z0-9_.]*\)'
      scope: variable.parameter.build2.buildfile
    - match: '(\$)(\()'
      captures:
        1: variable.other.build2.buildfile
        2: punctuation.section.group.begin.build2.buildfile
      push: eval_context

  function_calls:
    - match: '(\$[a-zA-Z][a-zA-Z0-9_.]*)(\()'
      captures:
        1: variable.function.build2.buildfile
        2: punctuation.section.group.begin.build2.buildfile
      push: eval_context

  eval_contexts:
    - match: '\('
      scope: punctuation.section.group.begin.build2.buildfile
      push: eval_context
  eval_context:
    - meta_scope: meta.group.build2.buildfile.eval_context
    - include: adhoc_inputs_and_outputs
    - include: line_mode
    - include: value_attributes
    - match: '\)'
      scope: punctuation.section.group.end.build2.buildfile
      pop: true
    - match: '(\|\||&&|==|<=|>=|!=|[?:!<>])'
      scope: keyword.operator.logical.build2.buildfile
    - match: '[|,]'
      scope: keyword.operator.build2.buildfile

  name_generations:
    - match: '\{'
      scope: punctuation.section.brace.begin.build2.buildfile.name_generation
      push: name_generation
  name_generation:
    - meta_scope: meta.name_generation.build2.buildfile
    - match: '(\})(\{)'
    - include: name_pattern

  value_attributes:
    - match: '\['
      scope: punctuation.section.brackets.begin.build2.buildfile
      push: value_attribute
  value_attribute:
    - meta_scope: meta.brackets.build2.buildfile.value_attribute
    - match: '\b(bool|uint64|uint64s|string|strings|path|paths|dir_path|dir_paths|name|names|name_pair|project_name|target_triplet)\b'
      scope: storage.type.build2.buildfile.config
    - include: line_mode
    - match: '\]'
      scope: punctuation.section.brackets.end.build2.buildfile
      pop: true
    - match: '@'
      scope: keyword.operator.build2.buildfile

  adhoc_recipes:
    - match: '^\s*(\%)\s*(update|test|clean|install|uninstall)\s*$'
      captures:
        1: constant.character.build2.buildfile.adhoc
        2: keyword.declaration.impl.build2.buildfile.adhoc
    - match: '^\s*(\{\{)\s*$'
      scope: punctuation.section.braces.begin.build2.buildfile.adhoc
      push: adhoc_recipe
  adhoc_recipe:
    - meta_scope: meta.braces.build2.buildfile.adhoc
    - include: multi_line_comments
    - include: line_comments
    - include: adhoc_recipe_diag_commands
    - include: adhoc_recipe_commands
    - include: function_calls
    - include: variable_expansions
    - include: eval_contexts
    - include: double_quotes
    - include: single_quotes
    - match: '^\s*(\}\})\s*$'
      scope: punctuation.section.braces.end.build2.buildfile.adhoc
      pop: true

  adhoc_inputs_and_outputs:
    - match: '(\$)(\<|\>)(\[)([0-9]+)(\])'
      captures:
        1: variable.parameter.build2.buildfile
        2: variable.parameter.build2.buildfile
        3: punctuation.section.brackets.begin.build2.buildfile.adhoc.io
        4: constant.numeric.build2.buildfile
        5: punctuation.section.brackets.end.build2.buildfile.adhoc.io
    - match: '(\$)(\<|\>)'
      captures:
        1: punctuation.definition.variable.build2.buildfile
        2: keyword.operator.build2.buildfile.adhoc.io

  adhoc_recipe_diag_commands:
    - match: '\b(diag)\b'
      scope: keyword.control.import.build2.buildfile
      push: adhoc_recipe_diag_command
  adhoc_recipe_diag_command:
    - meta_scope: meta.directive.build2.buildfile.adhoc
    - include: adhoc_inputs_and_outputs
    - include: line_mode

  adhoc_recipe_commands:
    - match: '\b(cat|cp|date|diff|echo|env|exit|false|ln|mkdir|mv|rm|rmdir|sed|set|sleep|test|touch|true)\b'
      scope: keyword.operator.word.build2.buildfile
      push: adhoc_recipe_command
  adhoc_recipe_command:
    - meta_scope: meta.directive.build2.buildfile.adhoc
    - include: adhoc_inputs_and_outputs
    - include: line_mode