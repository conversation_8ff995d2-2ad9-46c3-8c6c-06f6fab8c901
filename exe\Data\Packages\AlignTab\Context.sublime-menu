[
    { "caption": "-", "id": "aligntab_start" },
    {
        "id": "aligntab",
        "caption": "AlignTab\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t[...]  ",
        "children": [
            // AlignTab: Patterns
            { "caption": "Spaces       \t\t\t\t Pattern", "command": "align_tab", "args": { "user_input": "\\s*/l1l0" } },
            { "caption": "-" },
            // AlignTab: Align by *
            { "caption": "Vertical Bars\t\t\t\t | ",  "command": "align_tab", "args": { "user_input": "\\|"   } },
            { "caption": "Colon        \t\t\t\t : ",  "command": "align_tab", "args": { "user_input": "\\:"   } },
            { "caption": "Hyphen       \t\t\t\t - ",  "command": "align_tab", "args": { "user_input": "\\-"   } },
            { "caption": "Equal        \t\t\t\t = ",  "command": "align_tab", "args": { "user_input": "\\="   } },
            { "caption": "Commas       \t\t\t\t , ",  "command": "align_tab", "args": { "user_input": "\\,"   } },
            { "caption": "Hash         \t\t\t\t # ",  "command": "align_tab", "args": { "user_input": "\\#"   } },
            { "caption": "Ampersands   \t\t\t\t & ",  "command": "align_tab", "args": { "user_input": " \\& " } },
            { "caption": "Chevron      \t\t\t\t > ",  "command": "align_tab", "args": { "user_input": "\\>"   } },
            { "caption": "Arrow        \t\t\t\t -> ", "command": "align_tab", "args": { "user_input": "\\->"  } },
            { "caption": "-" },
            // AlignTab: Align by * ( First Occurrence Only )
            { "caption": "(First Bar)     \t\t\t\t | ", "command": "align_tab", "args": { "user_input": "\\|/f" } },
            { "caption": "(First Colon)   \t\t\t\t : ", "command": "align_tab", "args": { "user_input": "\\:/f" } },
            { "caption": "(First Hyphen)  \t\t\t\t - ", "command": "align_tab", "args": { "user_input": "\\-/f" } },
            { "caption": "(First Equal)   \t\t\t\t = ", "command": "align_tab", "args": { "user_input": "\\=/f" } },
            { "caption": "(First Hash)    \t\t\t\t # ", "command": "align_tab", "args": { "user_input": "\\#/f" } },
        ]
    }
]
