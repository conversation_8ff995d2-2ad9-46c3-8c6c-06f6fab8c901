%YAML 1.2
---
name: build2 repository list manifest
file_extensions:
  - repositories.manifest
scope: source.build2.repositories.manifest
contexts:
  main:
    - match: '^([\s]*)#'
      scope: punctuation.definition.comment.build2.repositories.manifest
      push: line_comment
    - match: ':'
      scope: punctuation.definition.variable.build2.repositories.manifest
      push: format_version
    - match: location
      scope: variable.language.build2.repositories.manifest
      set: [location, define]
    - match: type
      scope: variable.language.build2.repositories.manifest
      set: [type, define]
    - match: role
      scope: variable.language.build2.repositories.manifest
      set: [role, define]
    - match: trust
      scope: variable.language.build2.repositories.manifest
      set: [trust, define]
    - match: url
      scope: variable.language.build2.repositories.manifest
      push: [url, define]
    - match: email
      scope: variable.language.build2.repositories.manifest
      push: [email, define]
    - match: '\b(summary)\b'
      scope: variable.language.build2.repositories.manifest
      set: [summary, define]
    - match: description
      scope: variable.language.build2.repositories.manifest
      push: [description, define]
    - match: certificate
      scope: variable.language.build2.repositories.manifest
      push: [certificate, define]
    - match: fragment
      scope: variable.language.build2.repositories.manifest
      push: [fragment, define]
    - match: '\b[^:\s]+\b'
      scope: source.build2.repositories.manifest
      push: [other_variable, define]

  line_comment:
    - meta_scope: comment.line.build2.repositories.manifest
    - match: $
      pop: true

  define:
    - match: '(:)(\\\n)'
      set: [main, multi_line_mode]
      captures:
        1: punctuation.definition.variable.build2.repositories.manifest
        2: constant.character.escape.build2.repositories.manifest
    - match: ':'
      scope: punctuation.definition.variable.build2.repositories.manifest
      pop: true

  line_mode:
    - match: '\\\\'
      scope: constant.character.escape.build2.repositories.manifest
    - match: '\\\n'
      scope: constant.character.escape.build2.repositories.manifest
    - match: '\n'
      pop: true

  multi_line_mode:
    - meta_scope: text.build2.repositories.manifest
    - match: '\\\\'
      scope: constant.character.escape.build2.repositories.manifest
    - match: '^\\\n'
      scope: constant.character.escape.build2.repositories.manifest
      pop: true
    - match: '^<EOF>$'
      scope: constant.character.escape.build2.repositories.manifest
      pop: true
    - match: '\\\n'
      scope: constant.character.escape.build2.repositories.manifest

  documentation_comments:
    - match: '\\;'
      scope: constant.character.escape.build2.repositories.manifest
    - match: ';'
      scope: punctuation.definition.comment.line.documentation.build2.repositories.manifest
      push: documentation_line_comment
  documentation_line_comment:
    - meta_scope: comment.line.documentation.build2.repositories.manifest
    - match: $
      pop: true

  other_variable:
    - include: line_mode
    - include: documentation_comments

  format_version:
    - match: '\b[0-9]*\b'
      scope: constant.numeric.build2.repositories.manifest
    - match: $
      pop: true

  location:
    - include: line_mode

  type:
    - include: line_mode
    - match: '\b(pkg|dir|git)\b'
      scope: constant.language.build2.repositories.manifest

  role:
    - include: line_mode
    - match: '\b(base|prerequisite|complement)\b'
      scope: constant.language.build2.repositories.manifest

  trust:
    - include: line_mode
    - match: '[A-Fa-f0-9]{64}'
      scope: constant.numeric.build2.repositories.manifest

  url:
    - include: line_mode

  email:
    - include: line_mode
    - include: documentation_comments

  summary:
    - include: line_mode

  description:
    - include: line_mode

  certificate:
    - include: line_mode

  fragment:
    - include: line_mode