{
    // If an exception occurs when processing a function, should it be reraised
    // so the user gets feedback? This is really only useful to those writing
    // functions. The average user just wants the plugin to work, so let's not reraise
    // the exception
    "reraise_exceptions": true,

    // If you want to have a syntax applied when new files are created, set
    // "new_file_syntax" to the name of the syntax to use. The format is exactly the
    // same as "syntax" in the rules below. For example, if you want to have a new
    // file use JavaScript syntax, set "new_file_syntax" to 'JavaScript/JavaScript'.
    "new_file_syntax": false,

    // Auto add extensions to language settings file in User folder.
    // Do not manually remove "ApplySyntax.ext-list" from the User folder
    // as it is used to track which extensions ApplySyntax added so
    // they can be removed later if needed.
    // "extenstions" are ignored by "match": "all" setting.
    "add_exts_to_lang_settings": true,

    // Control level of logging in the console.
    // (true|false|"verbose")
    "debug": true,

    // To specify a syntax to be used, use the syntax path.
    // You do not need to include `Packages/` nor do you need to include the extension.
    // So, if you the syntax you wanted was located at `Packages/MyPlugin/my_syntax.sublime-syntax`,
    // then you would specify `MyPlugin/my_syntax`. To make this easy, you can use of
    // the following commands in the command palette:
    //
    //.- `ApplySyntax: Browse Syntaxes`
    // - `ApplySyntax: Copy Current Syntax to Clipboard`
    //
    //.See documentation for more info.
    "default_syntaxes": [
        {
            // The MXML rules come before the generic XML rules because they
            // need syntax highlighting for the CDATA sections, but they do not
            // have any characteristics that distinguish themselves from XML
            // aside from their file extension
            "syntax": "MXML",
            "extensions": ["mxml"]
        },
        {
            "syntax": "XML/XML",
            "extensions": ["xml.dist"]
        },
        {
            // The Cucumber and RSpec rules come before Rails and Ruby because they end
            // in .rb and will be identified as Rails or Ruby otherwise.
            "syntax": "Cucumber/Cucumber Steps",
            "rules": [
                {"globmatch": "**/steps.rb"}
            ]
        },
        {
            "syntax": [
                "RSpec/RSpec",
                "RSpec (snippets and syntax)/Syntaxes/RSpec",
                "Better RSpec/Better RSpec",
                "RSpec Buddy/RSpec Buddy"
            ],
            "rules": [
                {"globmatch": "**/{*spec,spec/**/*}.rb"}
            ]
        },
        {
            // Will make .html.erb as HTML files
            "syntax": "Rails/HTML (Rails)",
            "extensions": ["html.erb"]
        },
        {
            "syntax": ["Rails/HAML", "Rails/Ruby Haml"],
            "extensions": ["haml"]
        },
        {
            // This is an example of using a custom function to decide whether or
            // not to apply this syntax. The source file should be in a plugin folder.
            // "name" is the function name and "source" is the file in which the
            // function is contained. The rule should be specified as such.
            "syntax": ["Rails/Ruby (Rails)", "Rails/Ruby on Rails"],
            "rules": [
                {"function": {"source": "ApplySyntax.as_plugins.is_rails_file"}}
            ]
        },
        {
            "syntax": "Ruby/Ruby",
            "extensions": ["thor", "rake", "simplecov", "jbuilder", "rb", "podspec", "rabl"],
            "rules": [
                {
                    "globmatch": [
                        "**/@(Gemfile|Capfile|Guardfile|[Rr]akefile|Berksfile|Thorfile|Podfile|config.ru|Cartfile?(.resolved)|Dangerfile)",
                        "**/Vagrantfile{,/**/*}"
                    ]
                },
                // Interpreter is a short hand way of doing:
                //      {"first_line": "^#\\!(?:.+)ruby"}
                {"interpreter": "ruby"}
            ]
        },
        // {
        //     // This is an example of using a contains rule to match files which
        //     // contain the specified string anywhere inside them. It's useful when
        //     // you can only determine which syntax to use by looking at what's
        //     // actually inside the file. In this case, since the file contains a
        //     // handlebar template, we switch its syntax highlighting to Handlebars.
        //     // Because I worry about performance, it is disabled by default.
        //     "syntax": "Handlebars/Handlebars",
        //     "match": "all",
        //     "rules": [
        //         {"globmatch": "**/*.html$"},
        //         {"contains": "<script [^>]*type=\"text\\/x-handlebars\"[^>]*>"}
        //     ]
        // },
        {
            // Meteor templates follow Handlebars syntax, but must end in .html; but they also always begin with `<template name=`
            "syntax": "Handlebars/Handlebars",
            "match": "all",
            "rules": [
                {"globmatch": "**/*.html"},
                {"first_line": "^<template name="}
            ]
        },
        {
            // Emblem syntax matches Ruby Slim
            "syntax": "Ruby Slim/Syntaxes/Ruby Slim",
            "extensions": ["emblem"]
        },
        {
            "syntax": "Blade/Blade",
            "extensions": ["blade.php"]
        },
        {
            "syntax": "PHP/PHP",
            "extensions": ["php3", "php4", "php5", "inc", "phtml"],
            "rules": [
                {"first_line": "^<\\?php"}
            ]
        },
        {
            "syntax": "PHP/Smarty",
            "extensions": ["tpl"]
        },
        {
            "syntax": "YAML/YAML",
            "extensions": [".gemrc", "yml", "yml.dist", "YAML-tmLanguage"]
        },
        {
            "syntax": "Git Formats/Git Config",
            "rules": [
                {"globmatch": "**/.git/config"}
            ]
        },
        {
            // This rule requires the INI plugin to be installed (via Package Control
            // or https://github.com/clintberry/sublime-text-2-ini)
            "syntax": "INI/INI",
            "extensions": ["ini.dist", ".npmrc"]
        },
        {
            "syntax": [
                "ShellScript/Bash",
                "ShellScript/Shell-Unix-Generic"
            ],
            "extensions": ["bash", "sh", "zsh", "bashrc", "ash", ".profile"],
            "rules": [
                {
                    "globmatch": [
                        "**/.bash*",
                        "**/.z@(shrc|shenv|profile|login|logout)*"
                    ]
                },
                {"interpreter": "bash"},
                {"interpreter": "zsh"}
            ]
        },
        {
            // Sublime recognizes .py files, but not if they don't have an extension,
            // just a shebang
            "syntax": "Python/Python",
            "extensions": ["py3", "pyw"],
            "rules": [
                {"interpreter": "python"}
            ]
        },
        {
            "syntax": ["JSON/JSON", "JavaScript/JSON"],
            "extensions": [
                ".jshintrc", ".jscsrc", ".csslintrc", ".eslintrc", ".bowerrc", "geojson", ".babelrc", ".stylelintrc"
            ]
        },
        {
            // .m files could be Objective-C or Matlab files
            // We first use a rather reliable regex from GitHub
            // https://github.com/github/linguist/blob/master/lib/linguist/heuristics.rb#L69)
            // to apply syntax for Objective-C files
            "syntax": "Objective-C/Objective-C",
            "match": "all",
            "rules": [
                {"globmatch": "**/*.m"},
                {"contains": "^\\s*(@(interface|class|protocol|property|end|synchronised|selector|implementation)\\b|#import\\s+.+\\.h[\">])"}
            ]
        },
        {
            // Then use the % regex to detect Matlab files
            "syntax": "Matlab/Matlab",
            "match": "all",
            "rules": [
                {"globmatch": "**/*.m"},
                {"contains": "(^|\n)\\s*%"}
            ]
        },
        {
            "syntax": "TypoScript/TypoScript",
            "rules": [
                {
                    "globmatch": [
                        "**/ext_conf_template.txt",
                        "**/ext_typoscript_@(setup|constants).txt",
                        "**/@(fileadmin|typo3|TypoScript)/**/@(setup|constants).txt"
                    ]
                }
            ]
        }
    ],
    "default_ext_trim": []
}
