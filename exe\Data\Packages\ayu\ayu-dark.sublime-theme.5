[
    //--------------------------------------------------------
    // Title Bar
    //--------------------------------------------------------
    // {
    //     "class": "title_bar",
    //     "settings": [
    //         "!ui_native_titlebar"
    //     ],
    //     "bg": "hsl(220, 30%, 10%)",
    //     "fg": "hsl(45, 6.5%, 75%)"
    // },
    {
        "class": "title_bar",
        "settings": [
            "!ui_native_titlebar",
            "ui_separator"
        ],
        "bg": "hsl(220, 30%, 7%)"
    },

    //--------------------------------------------------------
    // Sidebar -> Background
    //--------------------------------------------------------
    {
        "class": "sidebar_container",
        "content_margin": [5, 6, 0, 0 ],
        "layer0.inner_margin": [0, 54, 0, 0],
        "layer0.opacity": 1,
        "layer0.tint": "hsl(220, 30%, 9%)"
    },
    //--------------------------------------------------------
    // Sidebar -> NOT SURE WHAT THIS IS
    //--------------------------------------------------------
    // {
    //     "class": "sidebar_container",
    //     "settings": [
    //         "ui_separator"
    //     ],
    //     "layer0.tint": "hsl(220, 30%, 10%)",
    //     "layer1.texture": "ayu/assets/separator-sidebar.png",
    //     "layer1.inner_margin": [
    //         0,
    //         38,
    //         2,
    //         1
    //     ],
    //     "layer1.opacity": 1,
    //     "layer1.tint": "hsl(220, 20%, 15%)"
    // },
    //--------------------------------------------------------
    // Sidebar -> Options
    //--------------------------------------------------------
    {
        "class": "sidebar_tree",
        "indent_top_level": false,
        "row_padding": [
            20,
            4
        ],
        "dark_content": false,
        "spacer_rows": true,
        "indent_offset": 12,
        "indent": 16,
    },
    //--------------------------------------------------------
    // Sidebar -> Foreground -> Groups
    //--------------------------------------------------------
    {
        "class": "sidebar_heading",
        "color": "hsla(215, 90%, 60%, 1)",
        "font.bold": true,
        "font.size": 13
    },
    //--------------------------------------------------------
    // Sidebar -> Background -> Hover -> Files
    //--------------------------------------------------------
    {
        "class": "tree_row",
        "layer0.texture": "ayu/assets/tree-highlight.png",
        "layer0.tint": "hsla(220, 20%, 35%, 0)",
        "layer0.inner_margin": [
            8,
            4
        ],
        "layer0.opacity": 1,
        "layer1.texture": "ayu/assets/tree-highlight-border.png",
        "layer1.tint": "hsla(215, 100%, 60%, 0.5)",
        "layer1.inner_margin": [
            8,
            4
        ],
        "layer1.opacity": 0
    },
    {
        "class": "tree_row",
        "attributes": [
            "selectable",
            "hover"
        ],
        "layer0.tint": "hsla(220, 50%, 30%, 0.5)",
        "layer1.opacity": 1
    },
    //--------------------------------------------------------
    // Sidebar -> Background -> Selected -> Files
    //--------------------------------------------------------
    {
        "class": "tree_row",
        "attributes": [
            "selectable",
            "selected"
        ],
        // "layer0.tint": "hsla(220, 20%, 35%, 0.2)"
        // "layer0.tint": "hsla(155, 100%, 60%, 0.2)",
        "layer0.tint": "hsla(250, 100%, 65%, 0.5)",
    },
    {
        "class": "tree_row",
        "attributes": [
            "selectable",
            "selected",
            "hover"
        ],
        // "layer0.tint": "hsla(220, 20%, 35%, 0.25)"
        // "layer0.tint": "hsla(155, 100%, 60%, 0.3)",
    },
    //--------------------------------------------------------
    // Sidebar -> Foreground
    //--------------------------------------------------------
    {
        "class": "sidebar_label",
        "fg": "hsl(220, 10%, 55%)",
        "font.size": 12
    },
    //--------------------------------------------------------
    // Sidebar -> Foreground -> Hover
    //--------------------------------------------------------
    {
        "class": "sidebar_label",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["hover"]
        }],
        "fg": "hsl(0, 0%, 90%)"
    },
    //--------------------------------------------------------
    // Sidebar -> Foreground -> Selected
    //--------------------------------------------------------
    {
        "class": "sidebar_label",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["selected"] }],
        "fg": "hsl(0, 0%, 100%)"
    },
    //--------------------------------------------------------
    // Sidebar -> Foreground -> Folders
    //--------------------------------------------------------
    {
        "class": "sidebar_label",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["expandable"]
        }],
        "fg": "hsl(0, 0%, 70%)",
    },
    {
        "class": "sidebar_label",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["expandable"]
        }],
        "settings": [
            "bold_folder_labels"
        ],
        "font.bold": true
    },
    //--------------------------------------------------------
    // Sidebar -> Foreground -> Folders -> Hover
    //--------------------------------------------------------
    {
        "class": "sidebar_label",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["expandable", "hover"]
        }],
        "fg": "hsl(0, 0%, 90%)"
    },
    //--------------------------------------------------------
    // Sidebar -> Foreground -> Folders -> Expanded
    //--------------------------------------------------------
    {
        "class": "sidebar_label",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["expanded"]
        }],
        "fg": "hsl(0, 0%, 90%)"
    },
    {
        "class": "sidebar_label",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["expanded"]
        }],
        "settings": [
            "bold_folder_labels"
        ],
        "font.bold": true
    },
    {
        "class": "sidebar_label",
        "attributes": ["transient"],
        "font.italic": false
    },
    {
        "class": "sidebar_label",
        "parents": [
        {
            "class": "file_system_entry",
            "attributes": ["ignored"]
        }],
        "fg": "hsla(220, 10%, 35%, 0.5)"
    },
    {
        "class": "disclosure_button_control",
        "content_margin": [0, 0, 0, 0
        ]
    },
    //--------------------------------------------------------
    // Sidebar -> Close-Button -> Default
    //--------------------------------------------------------
    {
        "class": "close_button",
        "content_margin": [8, 8],
        // "layer0.texture": "ayu/assets/close.png",
        // "layer0.texture": "ayu/assets/close_custom.png",
        // "layer0.texture": "ayu/assets/<EMAIL>",
        // "layer0.texture": "ayu/assets/<EMAIL>",

        // "layer0.texture": "ayu/assets/<EMAIL>",
        "layer0.texture": "ayu/assets/<EMAIL>",

        // "layer0.texture": "ayu/assets/<EMAIL>",
        // "layer0.texture": "ayu/assets/<EMAIL>",
        "layer0.opacity": 0.6,
        // "layer0.inner_margin": [-15, 5, 0, 0 ],
        // "layer0.inner_margin": [0, 10, 0, -10], // adjust these values
        // "layer0.inner_margin": [0, 10, 0, -10],
        "layer0.tint": "hsl(220, 15%, 85%)",
    },
    //--------------------------------------------------------
    // Sidebar -> Close-Button -> Hover
    //--------------------------------------------------------
    {
        "class": "close_button",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["hover"]
        }],
        "layer0.texture": "ayu/assets/<EMAIL>",
        "layer0.opacity": 1,
    },
    //--------------------------------------------------------
    // Sidebar -> Close-Button -> Modified -> Yellow
    //--------------------------------------------------------
    {
        "class": "close_button",
        "attributes": ["dirty", "!added", "!deleted"],
        // "layer0.texture": "ayu/assets/dirty.png",
        // "layer0.texture": "ayu/assets/<EMAIL>",
        // "layer0.texture": "ayu/assets/<EMAIL>",
        "layer0.opacity": 1,
        "layer0.tint": "hsl(40, 75%, 60%)",
    },
    //--------------------------------------------------------
    // Sidebar -> Close-Button -> Unsaved -> Blue
    //--------------------------------------------------------
    {
        "class": "close_button",
        "attributes": ["added", "!deleted"],
        // "layer0.texture": "ayu/assets/dirty.png",
        "layer0.opacity": 1,
        "layer0.tint": "hsla(215, 90%, 60%, 1)",
    },
    //--------------------------------------------------------
    // Sidebar -> Close-Button -> Deleted -> Red + Italic
    //--------------------------------------------------------
    {
        "class": "close_button",
        "attributes": ["deleted"],
        // "layer0.texture": "ayu/assets/dirty.png",
        "layer0.opacity": 1,
        "layer0.tint": "hsl(340, 80%, 60%)",
    },
    //--------------------------------------------------------
    // Sidebar -> Close-Button -> Button -> Hover
    //--------------------------------------------------------
    {
        "class": "close_button",
        "attributes": [
            "hover"
        ],
        "layer0.opacity": 1,
        "layer0.tint": "hsl(340, 100%, 60%)"
    },
    //--------------------------------------------------------
    // Sidebar -> Folders -> Default
    //--------------------------------------------------------
    {
        "class": "icon_folder",
        "content_margin": [9, 9 ],
        "layer0.tint": "hsl(220, 30%, 10%)",
        "layer0.opacity": 0,
        "layer1.texture": "ayu/assets/folder.png",
        "layer1.tint": "hsla(220, 10%, 35%, 0.75)",
        "layer1.opacity": 1,
        "layer2.texture": "ayu/assets/folder-open.png",
        "layer2.tint": "hsl(40, 75%, 60%)",
        "layer2.opacity": 0
    },
    //--------------------------------------------------------
    // Sidebar -> Folders -> Expanded
    //--------------------------------------------------------
    {
        "class": "icon_folder",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["expanded"]
        }],
        "layer1.opacity": 0,
        "layer2.opacity": 1
    },
    //--------------------------------------------------------
    // Sidebar -> Folders -> Hover
    //--------------------------------------------------------
    {
        "class": "icon_folder",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["hover"]
        }],
        "layer1.tint": "hsl(40, 75%, 60%)"
    },
    //--------------------------------------------------------
    // Sidebar -> Folders -> Expanded + Hover
    //--------------------------------------------------------
    {
        "class": "icon_folder",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["expanded", "hover"]
        }],
        "layer2.texture":
        {
            "keyframes": [
                "ayu/assets/folder-open-1.png",
                "ayu/assets/folder-open-1.png",
                "ayu/assets/folder-open-2.png",
                "ayu/assets/folder-open-3.png",
                "ayu/assets/folder-open-4.png",
                "ayu/assets/folder-open-5.png",
                "ayu/assets/folder-open-5.png",
                "ayu/assets/folder-open-5.png",
                "ayu/assets/folder-open-6.png",
                "ayu/assets/folder-open-6.png",
                "ayu/assets/folder-open-6.png",
                "ayu/assets/folder-open-6.png",
                "ayu/assets/folder-open.png"
            ],
            "loop": false,
            "frame_time": 0.02
        },
        "layer1.opacity": 0,
        "layer2.opacity": 1
    },
    //--------------------------------------------------------
    // Sidebar -> Folders -> Selected
    //--------------------------------------------------------
    {
        "class": "icon_folder",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["selected"] }],
        "layer1.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_folder_loading",
        "layer1.texture":
        {
            "keyframes": [
                "ayu/assets/spinner11.png",
                "ayu/assets/spinner10.png",
                "ayu/assets/spinner9.png",
                "ayu/assets/spinner8.png",
                "ayu/assets/spinner7.png",
                "ayu/assets/spinner6.png",
                "ayu/assets/spinner5.png",
                "ayu/assets/spinner4.png",
                "ayu/assets/spinner3.png",
                "ayu/assets/spinner2.png",
                "ayu/assets/spinner1.png",
                "ayu/assets/spinner.png"
            ],
            "loop": true,
            "frame_time": 0.075
        },
        "layer1.tint": "hsl(40, 75%, 60%)",
        "layer0.opacity": 0,
        "content_margin": [8, 8 ]
    },
    {
        "class": "icon_folder_dup",
        "content_margin": [9, 9 ],
        "layer0.texture": "ayu/assets/folder.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "layer1.texture": "ayu/assets/folder-symlink.png",
        "layer1.tint": "hsl(220, 10%, 35%)",
        "layer1.opacity": 0.3
    },
    {
        "class": "icon_folder_dup",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["hover"]
        }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_folder_dup",
        "parents": [
        {
            "class": "tree_row",
            "attributes": ["expanded"]
        }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_file_type",
        "content_margin": [8, 8 ]
    },
    {
        "class": "vcs_status_badge",
        "attributes": ["ignored"],
        "layer0.tint": "hsla(220, 10%, 35%, 0.3)",
    },
    {
        "class": "vcs_status_badge",
        "attributes": ["added"],
        "layer0.tint": "hsla(105, 60%, 60%, 0.4)",
    },
    {
        "class": "vcs_status_badge",
        "attributes": ["modified"],
        "layer0.tint": "hsla(210, 100%, 70%, 0.4)",
    },
    {
        "class": "vcs_status_badge",
        "attributes": ["deleted"],
        "layer0.tint": "hsla(355, 85%, 70%, 0.4)",
    },
    {
        "class": "sheet_contents",
        "background_modifier": ""
    },
    {
        "class": "sheet_contents",
        "settings":
        {
            "inactive_sheet_dimming": true
        },
        "attributes": ["!highlighted"],
        "background_modifier": "blend(hsl(220, 30%, 7%) 0%)"
    },



    //--------------------------------------------------------
    // (Tabset) Container for the tabs (and tab control buttons)
    //--------------------------------------------------------
    {
        "class": "tabset_control",
        "mouse_wheel_switch": false,
        "connector_height": 1,
        "content_margin": [10, 1, 5, 0],
        "layer0.opacity": 1,
        // "layer0.tint": "hsl(0, 0%, 5%)",
        // "layer0.tint": "hsl(223.33, 50%, 17.647%)",

        "layer0.tint": "hsl(223.33, 20%, 17.647%)",

        "layer0.tint": "#33344c",
        "layer0.tint": "hsl(220, 25%, 25%)",
        "layer1.draw_center": false,
        "layer1.inner_margin": [1, 1, 1, 1, ],
        "layer1.opacity": 1,
        "tab_height": 40,
        "tab_min_width": 40,
        "tab_overlap": 0,
        "tab_width": 50,
        "tint_index": 1,
        "tint_modifier": "hsl(0, 0%, 5%)",
    },
    // {
    //     "class": "tabset_control",
    //     "settings": ["mouse_wheel_switches_tabs", "!enable_tab_scrolling"],
    //     "mouse_wheel_switch": true
    // },

    // Remove horizontal indentation when tab scrolling is disabled
    {
        "class": "tabset_control",
        "settings": ["!enable_tab_scrolling"],
        "content_margin": [0, 1, 5, 0],
    },

    //--------------------------------------------------------
    // (Tabs) Initialize Default Tab Style
    //--------------------------------------------------------
    // - Layer0 is generally used for additional elements or overlays. In
    //   this case, it doesn't have a specified tint, so it will not affect
    //   the appearance unless specified in a variant (like hover or
    //   active).
    // - Layer1 is usually the base layer or background.
    // - Layer2 typically represents the border.
    // - Layer3 often serves as a secondary border or highlight (top-border
    //   in this case).
    // - The accent_tint_index specifies the base color for the accent tint.
    // - The tint_index specifies which layer's color to use as the base
    //   color.
    // - The tint_modifier applies a modification to the color specified by
    //   tint_index.
    //--------------------------------------------------------
    // Tab -> Default: Background
    {
        // Reference:
        // - layer0: closebutton
        // - layer1: background
        // - layer2: border
        // - layer3: top-border
        // - tint_index: layer-index to use as base color
        "class": "tab_control",
        "layer0.opacity": 1,
        "layer1.opacity": 1,
        "layer1.tint": "hsla(140, 50%, 55%, 1)",
        "layer2.draw_center": false,
        "layer2.inner_margin": [0, 0, 1, 1],
        "layer2.opacity": 1,
        "layer2.tint": "hsl(0, 0%, 0%)",
        "layer3.draw_center": false,
        "layer3.inner_margin": [0, 2, 0, 0],
        "layer3.opacity": 0.4,
        "layer3.tint": "hsla(140, 50%, 55%, 1)",
        "content_margin": [12, 6, 10, 4],
        "accent_tint_index": 2,
        "tint_index": 1,
        "tint_modifier": "hsl(220, 25%, 15%)",
    },

    // Tab -> Default: Foreground
    {
        "class": "tab_label",
        // "fg": "hsla(50, 10%, 55%, 1)",
        "fg": "hsla(50, 10%, 80%, 1)",
        "opacity": 0.5,
        "font.italic": false,
        "font.bold": false,
        "font.size": 12
    },
    // Tab -> Default: Closebutton
    {
        // Reference:
        // - content_margin: button-size
        "class": "tab_close_button",
        "content_margin": [8, 8],
        "layer0.texture": "ayu/assets/<EMAIL>",
        "layer0.tint": "hsla(50, 10%, 80%, 1)",
        "layer0.opacity": 0.35,
    },

    //--------------------------------------------------------
    // (Tabs -> Overrides) Tab Hover
    //--------------------------------------------------------
    // - Affects all tabs except active/highlighted
    //--------------------------------------------------------
    // Tab -> Hover -> Foreground
    {
        "class": "tab_label",
        "parents": [
        {
            "class": "tab_control",
            "attributes": ["!selected", "!highlighted", "hover"]
        }],
        "opacity": 0.7,
    },
    // Tab -> Hover -> Background
    {
        "class"               : "tab_control",
        "attributes"          : ["!selected", "!highlighted", "hover"],
        "layer2.inner_margin" : [0, 0, 0, 3],
        "layer2.opacity"      : 1,
        "layer3.inner_margin" : [0, 2, 0, 0],
        "layer3.opacity"      : 1,
        "tint_modifier"       : "hsl(220, 25%, 10%)",
    },
    // Tab -> Hover -> Closebutton
    {
        "class": "tab_close_button",
        "parents": [
        {
            "class": "tab_control",
            "attributes": ["!selected", "!highlighted", "hover"]
        }],
        "layer0.opacity": 0.7,
    },



    //--------------------------------------------------------
    // (Tabs -> Overrides) Selected
    //--------------------------------------------------------
    // - Affects all selected tabs
    //--------------------------------------------------------
    // Tab -> Selected -> Foreground
    {
        "class": "tab_label",
        "parents": [
        {
            "class": "tab_control",
            "attributes": ["selected"]
        }],
        "opacity": 1,
    },
    // Tab -> Selected -> Background
    {
        "class": "tab_control",
        "attributes": ["selected"],
        "layer1.opacity": 1,
        "layer2.inner_margin": [0, 0, 0, 2],
        "layer2.tint": "hsl(250, 80%, 65%)",
        "layer2.opacity": 1,
        "layer3.inner_margin": [0, 2, 0, 0],
        "layer3.opacity": 1,
        "tint_modifier": "hsl(240, 4%, 6%)",
    },
    // Tab -> Selected -> Closebutton
    {
        "class": "tab_close_button",
        "parents": [
        {
            "class": "tab_control",
            "attributes": ["selected"]
        }],
        "layer0.texture": "ayu/assets/<EMAIL>",
        "layer0.opacity": 0.8,
    },

    //--------------------------------------------------------
    // (Tabs -> Overrides) Closebutton
    //--------------------------------------------------------
    // Closebutton -> Hover
    {
        "class": "tab_close_button",
        "settings": [
            "show_tab_close_buttons",
            "highlight_modified_tabs"
        ],
        "attributes": ["hover"],
        "layer0.texture": "ayu/assets/<EMAIL>",
        "layer0.opacity": 1,
        "layer0.tint": "hsl(340, 100%, 60%)"
    },



    //--------------------------------------------------------
    // (Tabs) Override -> Unsaved Tabs -> Blue
    //--------------------------------------------------------
    // Foreground
    {
        "class": "tab_label",
        "parents": [
        {
            "class": "tab_control",
            "attributes": ["added", "!deleted"]
        }],
        "fg": "hsla(215, 95%, 60%, 1)",
        // "opacity": 1,
    },
    // Background
    {
        "class": "tab_control",
        "attributes": ["added", "!deleted"],
        "layer3.tint": "hsla(215, 95%, 60%, 1)",
    },
    //--------------------------------------------------------
    // (Tabs) Override -> Modified Tabs -> Yellow
    //--------------------------------------------------------
    // Foreground
    {
        "class": "tab_label",
        "parents": [
        {
            "class": "tab_control",
            "attributes": ["dirty", "!added", "!deleted"]
        }],
        "fg": "hsl(40, 100%, 60%)",
    },
    // Background
    {
        "class": "tab_control",
        "attributes": ["dirty", "!added", "!deleted"],
        "layer3.tint": "hsl(40, 75%, 60%)",
    },
    //--------------------------------------------------------
    // (Tabs) Override -> Deleted File -> Red + Italic
    //--------------------------------------------------------
    // Foreground
    {
        "class": "tab_label",
        "parents": [
        {
            "class": "tab_control",
            "attributes": ["deleted"]
        }],
        "fg": "hsl(340, 100%, 60%)",
        "font.italic": true,
    },
    // Background
    {
        "class": "tab_control",
        "attributes": ["deleted"],
        "layer3.tint": "hsl(340, 80%, 60%)",
    },

    {
        "class": "scroll_tabs_left_button",
        "content_margin": [12, 15 ],
        "layer0.texture": "ayu/assets/arrow-left.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1
    },
    {
        "class": "scroll_tabs_left_button",
        "attributes": ["hover"],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "scroll_tabs_right_button",
        "content_margin": [12, 15 ],
        "layer0.texture": "ayu/assets/arrow-right.png",
        // "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1
    },
    {
        "class": "scroll_tabs_right_button",
        "attributes": ["hover"],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "show_tabs_dropdown_button",
        "content_margin": [12, 12 ],
        "layer0.texture": "ayu/assets/overflow-menu.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "layer0.inner_margin": [0, 0 ]
    },
    {
        "class": "show_tabs_dropdown_button",
        "attributes": ["hover"],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },

    //--------------------------------------------------------
    // (Dialog Windows) Dialog Style
    //--------------------------------------------------------
    // Dialog, margins, header, background, border
    {
        "class": "overlay_control",
        // Dialog -> Shadow
            "content_margin": [5, 5, 5, 5],
        // "layer0.texture": "ayu/assets/overlay-shadow.png",
        // "layer0.inner_margin": [15, 35, 15, 25 ],
        "layer0.tint": "hsla(0, 0%, 0%, 0.5)",
        "layer0.opacity": 1,
        // Dialog -> Border
        // "layer1.texture": "ayu/assets/overlay-border.png",
        // "layer1.inner_margin": [15, 35, 15, 25 ],
        "layer1.tint": "#2e3238",
        // "layer1.tint": "hsla(0, 0%, 50%, 1)",
        "layer1.opacity": 1,
        // Dialog -> Outer Background
        // "layer2.texture": "ayu/assets/overlay-bg.png",
        // "layer2.inner_margin": [15, 35, 15, 25 ],
        // "layer2.tint": "hsl(220, 25%, 10%)",
        "layer2.tint": "#2e3238",
        "layer2.opacity": 1,
    },

    //--------------------------------------------------------
    // (Mini Quick Panel: Ctrl+Shift+P) 
    //--------------------------------------------------------
    // Row -> Selected:
    {
        // Reference:
        // layer0: background (selected row)
        // layer1: border (selected row)
        "class": "mini_quick_panel_row",
        "layer0.texture": "ayu/assets/tree-highlight.png",
        "layer0.tint": "hsla(220, 20%, 35%, 0.25)",
        "layer0.inner_margin": [8, 4 ],
        "layer0.opacity": 0,
        "layer1.texture": "ayu/assets/tree-highlight-border.png",
        "layer1.tint": "hsla(220, 20%, 35%, 0.25)",
        "layer1.inner_margin": [8, 4 ],
        "layer1.opacity": 0,
    },
    // Row -> Selected -> Opacity
    {
        "class": "mini_quick_panel_row",
        "attributes": ["selected"],
        "layer0.opacity": 1,
        "layer1.opacity": 1
    },
    // Row -> Selected -> Hover
    {
        "class": "mini_quick_panel_row",
        "attributes": ["hover"],
        "layer0.opacity": 0.5,
        "layer1.opacity": 1
    },



    //--------------------------------------------------------
    // (Quick Panel) Ctrl+P
    //--------------------------------------------------------
    // Row -> Selected:
    {
        // Reference:
        // layer0: background (selected row)
        // layer1: border (selected row)
        "class": "quick_panel_row",
        "layer0.texture": "ayu/assets/tree-highlight.png",
        "layer0.tint": "hsla(220, 20%, 35%, 0.25)",
        // "layer0.tint": "#2e3238",
        // "layer0.inner_margin": [8, 4 ],
        "layer0.opacity": 0,
        // layer1: selected -> border
        "layer1.texture": "ayu/assets/tree-highlight-border.png",
        "layer1.tint": "hsla(220, 20%, 35%, 0.25)",
        "layer1.inner_margin": [8, 4 ],
        "layer1.opacity": 0
    },
    {
        "class": "quick_panel",
            "row_padding"       : [10,5,10,5],
        "layer0.tint": "hsl(220, 25%, 10%)",
        // "layer0.tint": "hsl(220, 25%, 10%)",
        "layer0.opacity": 1
    },
    {
        "class": "quick_panel",
        "parents": [{"class": "overlay_control"}],
        "row_padding": [13, 7 ],
        "layer0.tint": "hsl(220, 25%, 10%)",
        "layer0.opacity": 1
    },
    {
        "class": "quick_panel_row",
        "attributes": ["selected"],
        "layer0.opacity": 1,
        "layer1.opacity": 1
    },

        // //--------------------------------------------------------
        // //  Quick Panel (Ctrl+P)
        // //--------------------------------------------------------
        // {
        //     "class"             : "quick_panel",
        //     "row_padding"       : [10,5,10,5],
        //     "row_padding": [6, 4, 6, 4],
        //     "layer0.tint"       : "hsl(220, 10%, 20%)",
        //     "layer0.opacity"    : 1,

        // },
        // //--------------------------------------------------------
        // //  Quick Panel (Ctrl+P) -> Foreground
        // //--------------------------------------------------------
        // {
        //     "class"             : "quick_panel_label",
        //     "fg"                : "hsl(0, 0%, 85%)",
        //     "selected_fg"       : "hsl(0, 0%, 100%)",
        //     "match_fg"          : "hsl(220, 100%, 60%)",
        //     "selected_match_fg" : "hsl(40, 75%, 60%)",
        // },
        // //--------------------------------------------------------
        // //  Quick Panel (Ctrl+P) -> Background -> Selected
        // //--------------------------------------------------------
        // {
        //     "class"             : "quick_panel_row",
        //     "attributes"        : ["selected"],
        //     "layer0.tint"       : "hsl(220, 50%, 10%)",
        //     "layer0.opacity"    : 0.8,
        // },
        // //--------------------------------------------------------
        // //  Quick Panel (Ctrl+P) -> Background -> Hover
        // //--------------------------------------------------------
        // {
        //     "class"             : "quick_panel_row",
        //     "attributes"        : ["!selected","hover"],
        //     "layer0.tint"       : "hsl(220, 85%, 10%)",
        //     "layer0.opacity"    : 0.4,
        // },
    {
        "class": "quick_panel_label",
        "fg": "hsl(220, 10%, 35%)",
        "match_fg": "hsl(40, 75%, 60%)",
        "selected_fg": "hsl(45, 6.5%, 75%)",
        "selected_match_fg": "hsl(40, 75%, 60%)"
    },
    {
        "class": "quick_panel_label",
        "parents": [{"class": "overlay_control"}],
        "fg": "hsl(220, 10%, 35%)",
        "match_fg": "hsl(40, 75%, 60%)",
        "selected_fg": "hsl(45, 6.5%, 75%)",
        "selected_match_fg": "hsl(40, 75%, 60%)"
    },
    {
        "class": "quick_panel_path_label",
        "fg": "hsl(220, 10%, 35%)",
        "match_fg": "hsl(45, 6.5%, 75%)",
        "selected_fg": "hsl(220, 10%, 35%)",
        "selected_match_fg": "hsl(45, 6.5%, 75%)"
    },
    {
        "class": "quick_panel_detail_label",
        "link_color": "hsl(200, 100%, 65%)"
    },
    {
        "class": "grid_layout_control",
        "border_size": 0,
        "border_color": "hsl(220, 20%, 15%)"
    },
    {
        "class": "grid_layout_control",
        "settings": ["ui_separator"],
        "border_size": 0
    },
    {
        "class": "minimap_control",
        "settings": ["always_show_minimap_viewport"],
        "viewport_color": "hsl(220, 10%, 35%)",
        "viewport_opacity": 0.3
    },
    {
        "class": "minimap_control",
        "settings": ["!always_show_minimap_viewport"],
        "viewport_color": "hsl(220, 10%, 35%)",
        "viewport_opacity":
        {
            "target": 0,
            "speed": 4,
            "interpolation": "smoothstep"
        }
    },
    {
        "class": "minimap_control",
        "attributes": ["hover"],
        "settings": ["!always_show_minimap_viewport"],
        "viewport_opacity":
        {
            "target": 0.3,
            "speed": 4,
            "interpolation": "smoothstep"
        }
    },
    {
        "class": "fold_button_control",
        "layer0.texture": "ayu/assets/unfold.png",
        "layer0.opacity": 1,
        "layer0.inner_margin": 0,
        "layer0.tint": "hsl(220, 10%, 35%)",
        "content_margin": [8, 6, 8, 6 ]
    },
    {
        "class": "fold_button_control",
        "attributes": ["hover"],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "fold_button_control",
        "attributes": ["expanded"],
        "layer0.texture": "ayu/assets/fold.png"
    },
    {
        "class": "popup_shadow",
        "layer0.texture": "ayu/assets/popup-shadow.png",
        "layer0.inner_margin": [14, 11, 14, 15 ],
        "layer0.opacity": 1,
        "layer0.tint": "hsla(0, 0%, 0%, 0.6)",
        "layer0.draw_center": false,
        "layer1.texture": "ayu/assets/popup-border.png",
        "layer1.inner_margin": [14, 11, 14, 15 ],
        "layer1.opacity": 1,
        "layer1.tint": "hsl(220, 20%, 15%)",
        "layer1.draw_center": false,
        "content_margin": [10, 7, 10, 13 ]
    },
    {
        "class": "popup_control",
        "layer0.texture": "ayu/assets/popup-bg.png",
        "layer0.inner_margin": [4, 4, 4, 4 ],
        "layer0.opacity": 1,
        "layer0.tint": "hsl(220, 25%, 10%)",
        "content_margin": [0, 4 ]
    },
    {
        "class": "auto_complete",
        "row_padding": [5, 0 ]
    },
    {
        "class": "table_row",
        "layer0.texture": "ayu/assets/tree-highlight.png",
        "layer0.tint": "hsla(220, 20%, 35%, 0.25)",
        "layer0.inner_margin": [8, 4 ],
        "layer0.opacity": 0,
        "layer1.texture": "ayu/assets/tree-highlight-border.png",
        "layer1.tint": "hsla(220, 20%, 35%, 0.25)",
        "layer1.inner_margin": [8, 4 ],
        "layer1.opacity": 0
    },
    {
        "class": "table_row",
        "attributes": ["selected"],
        "layer0.opacity": 1,
        "layer1.opacity": 1
    },
    {
        "class": "auto_complete_label",
        "fg": "transparent",
        "match_fg": "hsl(40, 75%, 60%)",
        "selected_fg": "transparent",
        "selected_match_fg": "hsl(40, 75%, 60%)",
        "fg_blend": true
    },
    {
        "class": "auto_complete_hint",
        "opacity": 0.7,
        "font.italic": true
    },
    {
        "class": "kind_container",
        "layer0.texture": "ayu/assets/kind-bg.png",
        "layer0.tint": "hsl(220, 25%, 10%)",
        "layer0.inner_margin": [4, 4, 7, 4 ],
        "layer0.opacity": 0,
        "layer1.texture": "ayu/assets/kind-bg.png",
        "layer1.tint": "hsla(220, 25%, 10%, 0)",
        "layer1.inner_margin": [4, 4, 7, 4 ],
        "layer1.opacity": 0.3,
        "layer2.texture": "ayu/assets/kind-border.png",
        "layer2.tint": "hsla(220, 25%, 10%, 0)",
        "layer2.inner_margin": [4, 4, 7, 4 ],
        "layer2.opacity": 0.1,
        "content_margin": [4, 0, 6, 0 ]
    },
    {
        "class": "kind_label",
        "font.size": "1rem",
        "font.bold": true,
        "font.italic": true,
        "color": "hsl(220, 10%, 35%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "quick_panel"}],
        "font.size": "1.1rem"
    },
    {
        "class": "kind_container kind_function",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(30, 100%, 65%)",
        "layer2.tint": "hsl(30, 100%, 65%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_function"}],
        "color": "hsl(30, 100%, 65%)"
    },
    {
        "class": "kind_container kind_keyword",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(25, 100%, 60%)",
        "layer2.tint": "hsl(25, 100%, 60%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_keyword"}],
        "color": "hsl(25, 100%, 60%)"
    },
    {
        "class": "kind_container kind_markup",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(200, 100%, 65%)",
        "layer2.tint": "hsl(200, 100%, 65%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_markup"}],
        "color": "hsl(200, 100%, 65%)"
    },
    {
        "class": "kind_container kind_namespace",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(200, 100%, 65%)",
        "layer2.tint": "hsl(200, 100%, 65%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_namespace"}],
        "color": "hsl(200, 100%, 65%)"
    },
    {
        "class": "kind_container kind_navigation",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(35, 70%, 70%)",
        "layer2.tint": "hsl(35, 70%, 70%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_navigation"}],
        "color": "hsl(35, 70%, 70%)"
    },
    {
        "class": "kind_container kind_snippet",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(355, 80%, 70%)",
        "layer2.tint": "hsl(355, 80%, 70%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_snippet"}],
        "color": "hsl(355, 80%, 70%)"
    },
    {
        "class": "kind_container kind_type",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(200, 100%, 65%)",
        "layer2.tint": "hsl(200, 100%, 65%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_type"}],
        "color": "hsl(200, 100%, 65%)"
    },
    {
        "class": "kind_container kind_variable",
        "layer0.opacity": 1,
        "layer1.tint": "hsla(210, 15%, 70%, 0.5)",
        "layer2.tint": "hsla(210, 15%, 70%, 0.5)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_variable"}],
        "color": "hsla(210, 15%, 70%, 0.5)"
    },
    {
        "class": "kind_container kind_color_redish",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(355, 80%, 70%)",
        "layer2.tint": "hsl(355, 80%, 70%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_color_redish"}],
        "color": "hsl(355, 80%, 70%)"
    },
    {
        "class": "kind_container kind_color_orangish",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(25, 100%, 60%)",
        "layer2.tint": "hsl(25, 100%, 60%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_color_orangish"}],
        "color": "hsl(25, 100%, 60%)"
    },
    {
        "class": "kind_container kind_color_yellowish",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(30, 100%, 65%)",
        "layer2.tint": "hsl(30, 100%, 65%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_color_yellowish"}],
        "color": "hsl(30, 100%, 65%)"
    },
    {
        "class": "kind_container kind_color_greenish",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(80, 65%, 55%)",
        "layer2.tint": "hsl(80, 65%, 55%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_color_greenish"}],
        "color": "hsl(80, 65%, 55%)"
    },
    {
        "class": "kind_container kind_color_cyanish",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(160, 60%, 75%)",
        "layer2.tint": "hsl(160, 60%, 75%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_color_cyanish"}],
        "color": "hsl(160, 60%, 75%)"
    },
    {
        "class": "kind_container kind_color_bluish",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(200, 100%, 65%)",
        "layer2.tint": "hsl(200, 100%, 65%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_color_bluish"}],
        "color": "hsl(200, 100%, 65%)"
    },
    {
        "class": "kind_container kind_color_purplish",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(270, 100%, 80%)",
        "layer2.tint": "hsl(270, 100%, 80%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_color_purplish"}],
        "color": "hsl(270, 100%, 80%)"
    },
    {
        "class": "kind_container kind_color_pinkish",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(20, 85%, 70%)",
        "layer2.tint": "hsl(20, 85%, 70%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_color_pinkish"}],
        "color": "hsl(20, 85%, 70%)"
    },
    {
        "class": "kind_container kind_color_dark",
        "layer0.opacity": 1,
        "layer1.tint": "hsl(220, 10%, 35%)",
        "layer2.tint": "hsl(220, 10%, 35%)"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_color_dark"}],
        "color": "hsl(220, 10%, 35%)"
    },
    {
        "class": "kind_container kind_color_light",
        "layer0.opacity": 1,
        "layer1.tint": "white",
        "layer2.tint": "white"
    },
    {
        "class": "kind_label",
        "parents": [{"class": "kind_container kind_color_light"}],
        "color": "hsl(0, 0%, 33.333%)"
    },
    {
        "class": "symbol_container",
        "content_margin": [
            4,
            3,
            4,
            3
        ]
    },
    {
        "class": "trigger_container",
        "content_margin": [
            4,
            3,
            4,
            3
        ]
    },
    {
        "class": "auto_complete_detail_pane",
        "layer0.opacity": 1,
        "layer0.tint": "hsl(220, 25%, 10%)",
        "layer1.opacity": 1,
        "layer1.tint": "hsl(220, 25%, 10%)",
        "content_margin": [
            8,
            10,
            8,
            5
        ]
    },
    {
        "class": "auto_complete_kind_name_label",
        "font.size": "0.9rem",
        "font.italic": true,
        "border_color": "hsl(220, 10%, 35%)"
    },
    {
        "class": "auto_complete_details",
        "background_color": "hsl(220, 25%, 10%)",
        "monospace_background_color": "hsl(220, 25%, 10%)"
    },
    {
        "class": "panel_control",
        "layer0.tint": "hsl(220, 30%, 10%)",
        "layer0.opacity": 1,
        "content_margin": [
            0,
            5
        ]
    },
    {
        "class": "panel_control",
        "settings": [
            "ui_separator"
        ],
        "layer0.tint": "hsl(220, 30%, 10%)",
        "layer1.texture": "ayu/assets/separator-top.png",
        "layer1.tint": "hsl(220, 20%, 15%)",
        "layer1.inner_margin": [
            1,
            2,
            1,
            0
        ],
        "layer1.opacity": 1
    },
    {
        "class": "panel_grid_control"
    },
    {
        "class": "panel_close_button",
        "layer0.texture": "ayu/assets/close.png",
        "layer0.opacity": 1,
        "layer0.tint": "hsl(220, 10%, 35%)",
        "content_margin": [
            0,
            0
        ]
    },
    {
        "class": "panel_close_button",
        "attributes": [
            "hover"
        ],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "status_bar",
        "layer0.texture": "",
        "layer0.tint": "hsl(220, 30%, 10%)",
        "layer0.opacity": 1,
        "layer1.texture": "ayu/assets/separator-top.png",
        "layer1.tint": "hsl(220, 20%, 15%)",
        "layer1.inner_margin": [
            1,
            2,
            1,
            0
        ],
        "content_margin": [
            10,
            2
        ]
    },
    {
        "class": "status_bar",
        "settings": [
            "ui_separator"
        ],
        "layer0.tint": "hsl(220, 30%, 10%)",
        "layer1.opacity": 1
    },
    {
        "class": "panel_button_control",
        "layer0.texture": "ayu/assets/switch-panel.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1
    },
    {
        "class": "panel_button_control",
        "attributes": [
            "hover"
        ],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "status_container",
        "content_margin": [
            0,
            5
        ]
    },
    {
        "class": "status_button",
        "min_size": [
            100,
            0
        ]
    },
    {
        "class": "vcs_branch_icon",
        "layer0.tint": "hsl(220, 10%, 35%)"
    },
    {
        "class": "vcs_changes_annotation",
        "border_color": "hsla(220, 10%, 35%, 0.7)"
    },
    {
        "class": "dialog",
        "layer0.tint": "hsl(220, 30%, 10%)",
        "layer0.opacity": 1
    },
    {
        "class": "progress_bar_control",
        "layer0.tint": "hsl(220, 30%, 10%)",
        "layer0.opacity": 1
    },
    {
        "class": "progress_gauge_control",
        "layer0.tint": "hsl(40, 75%, 60%)",
        "layer0.opacity": 1,
        "content_margin": [
            0,
            6
        ]
    },
    {
        "class": "scroll_area_control",
        "settings": [
            "overlay_scroll_bars"
        ],
        "overlay": true
    },
    {
        "class": "scroll_area_control",
        "settings": [
            "!overlay_scroll_bars"
        ],
        "overlay": false
    },
    {
        "class": "scroll_bar_control",
        "layer0.tint": "hsl(220, 30%, 10%)",
        "layer0.opacity": 1,
        "layer1.texture": "ayu/assets/scrollbar-vertical-wide.png",
        "layer1.tint": "hsl(220, 10%, 35%)",
        "layer1.opacity": 0.1,
        "layer1.inner_margin": [
            0,
            10
        ]
    },
    {
        "class": "scroll_bar_control",
        "parents": [{"class": "overlay_control"}],
        "layer0.tint": "hsl(220, 25%, 10%)"
    },
    {
        "class": "scroll_bar_control",
        "attributes": [
            "horizontal"
        ],
        "layer1.texture": "ayu/assets/scrollbar-horizontal-wide.png",
        "layer1.inner_margin": [
            10,
            0
        ]
    },
    {
        "class": "scroll_bar_control",
        "settings": [
            "overlay_scroll_bars"
        ],
        "layer0.opacity": 0,
        "layer1.texture": "ayu/assets/scrollbar-vertical.png",
        "layer1.inner_margin": [
            4,
            6,
            6,
            6
        ]
    },
    {
        "class": "scroll_bar_control",
        "settings": [
            "overlay_scroll_bars",
            "ui_wide_scrollbars"
        ],
        "layer0.texture": "ayu/assets/scrollbar-vertical-wide.png"
    },
    {
        "class": "scroll_bar_control",
        "settings": [
            "overlay_scroll_bars"
        ],
        "attributes": [
            "horizontal"
        ],
        "layer0.opacity": 0,
        "layer1.texture": "ayu/assets/scrollbar-horizontal.png",
        "layer1.inner_margin": [
            6,
            4,
            6,
            6
        ]
    },
    {
        "class": "scroll_bar_control",
        "attributes": [
            "horizontal"
        ],
        "settings": [
            "overlay_scroll_bars",
            "ui_wide_scrollbars"
        ],
        "layer0.texture": "ayu/assets/scrollbar-horizontal-wide.png"
    },
    {
        "class": "scroll_track_control",
        "layer0.tint": "hsl(220, 30%, 10%)",
        "layer0.opacity": 1
    },
    {
        "class": "scroll_corner_control",
        "layer0.tint": "hsl(220, 30%, 10%)",
        "layer0.opacity": 1
    },
    {
        "class": "puck_control",
        "layer0.texture": "ayu/assets/scrollbar-vertical-wide.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 0.3,
        "layer0.inner_margin": [
            0,
            10
        ],
        "content_margin": [
            6,
            12
        ]
    },
    {
        "class": "puck_control",
        "attributes": [
            "horizontal"
        ],
        "layer0.texture": "ayu/assets/scrollbar-horizontal-wide.png",
        "layer0.inner_margin": [
            10,
            0
        ],
        "content_margin": [
            12,
            6
        ]
    },
    {
        "class": "puck_control",
        "settings": [
            "overlay_scroll_bars"
        ],
        "layer0.texture": "ayu/assets/scrollbar-vertical.png",
        "layer0.inner_margin": [
            4,
            6,
            6,
            6
        ],
        "content_margin": [
            5,
            20
        ]
    },
    {
        "class": "puck_control",
        "settings": [
            "overlay_scroll_bars",
            "ui_wide_scrollbars"
        ],
        "layer0.texture": "ayu/assets/scrollbar-vertical-wide.png"
    },
    {
        "class": "puck_control",
        "settings": [
            "overlay_scroll_bars"
        ],
        "attributes": [
            "horizontal"
        ],
        "layer0.texture": "ayu/assets/scrollbar-horizontal.png",
        "layer0.inner_margin": [
            6,
            4,
            6,
            6
        ],
        "content_margin": [
            20,
            5
        ]
    },
    {
        "class": "puck_control",
        "attributes": [
            "horizontal"
        ],
        "settings": [
            "overlay_scroll_bars",
            "ui_wide_scrollbars"
        ],
        "layer0.texture": "ayu/assets/scrollbar-horizontal-wide.png"
    },
    {
        "class": "text_line_control",
        "layer0.texture": "ayu/assets/input-bg.png",
        "layer0.opacity": 1,
        "layer0.inner_margin": [
            10,
            8
        ],
        "layer0.tint": "hsl(220, 25%, 10%)",
        "layer1.texture": "ayu/assets/input-border.png",
        "layer1.opacity": 1,
        "layer1.inner_margin": [
            10,
            8
        ],
        "layer1.tint": "hsl(220, 20%, 15%)",
        "content_margin": [
            10,
            7,
            10,
            5
        ]
    },
    {
        "class": "text_line_control",
        "parents": [{"class": "overlay_control"}],
        "layer0.texture": "",
        "layer0.opacity": 0,
        "layer1.texture": "ayu/assets/input-prompt.png",
        "layer1.opacity": 1,
        "layer1.tint": "hsl(220, 10%, 35%)",
        "layer1.inner_margin": [
            36,
            26,
            0,
            0
        ],
        "content_margin": [
            38,
            5,
            10,
            5
        ]
    },
    {
        "class": "text_line_control",
        "parents": [{"class": "overlay_control goto_file"}],
        "layer1.texture": "ayu/assets/input-search.png"
    },
    {
        "class": "text_line_control",
        "parents": [{"class": "overlay_control command_palette"}],
        "layer1.texture": "ayu/assets/input-command.png"
    },
    {
        "class": "text_line_control",
        "parents": [{"class": "overlay_control goto_symbol"}],
        "layer1.texture": "ayu/assets/input-symbol.png"
    },
    {
        "class": "text_line_control",
        "parents": [{"class": "overlay_control goto_symbol_in_project"}],
        "layer1.texture": "ayu/assets/input-symbol.png"
    },
    {
        "class": "text_line_control",
        "parents": [
        {
            "class": "overlay_control goto_word"
        }],
        "layer1.texture": "ayu/assets/input-word.png"
    },
    {
        "class": "dropdown_button_control",
        "content_margin": [
            12,
            12
        ],
        "layer0.texture": "ayu/assets/overflow-menu.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1
    },
    {
        "class": "dropdown_button_control",
        "attributes": [
            "hover"
        ],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "button_control",
        "content_margin": [15, 9, 15, 10 ],
        "min_size": [60, 0 ],
        "layer0.tint": "hsl(40, 75%, 60%)",
        "layer0.texture": "ayu/assets/input-bg.png",
        "layer0.inner_margin": [10, 8 ],
        "layer0.opacity": 0
    },
    {
        "class": "button_control",
        "attributes": ["hover"],
        "layer0.opacity": 1
    },
    {
        "class": "icon_button_control",
        "layer0.tint": [0, 0, 0 ],
        "layer0.opacity": 0,
        "layer2.tint": "hsl(45, 6.5%, 75%)",
        "layer2.opacity":
        {
            "target": 0,
            "speed": 10,
            "interpolation": "smoothstep"
        },
        "content_margin": [10, 5 ]
    },
    {
        "class": "icon_regex",
        "layer0.texture": "ayu/assets/regex.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [12, 12 ]
    },
    {
        "class": "icon_regex",
        "parents": [
        {
            "class": "icon_button_control",
            "attributes": ["selected"]
        }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_case",
        "layer0.texture": "ayu/assets/matchcase.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [12, 12 ]
    },
    {
        "class": "icon_case",
        "parents": [
        {
            "class": "icon_button_control",
            "attributes": ["selected"]
        }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_whole_word",
        "layer0.texture": "ayu/assets/word.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [12, 12 ]
    },
    {
        "class": "icon_whole_word",
        "parents": [
        {
            "class": "icon_button_control",
            "attributes": ["selected"]
        }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_wrap",
        "layer0.texture": "ayu/assets/wrap.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [12, 12 ]
    },
    {
        "class": "icon_wrap",
        "parents": [
        {
            "class": "icon_button_control",
            "attributes": ["selected"]
        }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_in_selection",
        "layer0.texture": "ayu/assets/inselection.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [12, 12 ]
    },
    {
        "class": "icon_in_selection",
        "parents": [
        {
            "class": "icon_button_control",
            "attributes": ["selected"]
        }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_highlight",
        "layer0.texture": "ayu/assets/highlight.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [12, 12 ]
    },
    {
        "class": "icon_highlight",
        "parents": [
        {
            "class": "icon_button_control",
            "attributes": ["selected"]
        }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_preserve_case",
        "layer0.texture": "ayu/assets/replace-preserve-case.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [12, 12 ]
    },
    {
        "class": "icon_preserve_case",
        "parents": [
        {
            "class": "icon_button_control",
            "attributes": ["selected"]
        }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_context",
        "layer0.texture": "ayu/assets/context.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [12, 12 ]
    },
    {
        "class": "icon_context",
        "parents": [
        {
            "class": "icon_button_control",
            "attributes": ["selected"]
        }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_use_buffer",
        "layer0.texture": "ayu/assets/buffer.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [12, 12 ]
    },
    {
        "class": "icon_use_buffer",
        "parents": [
        {
            "class": "icon_button_control",
            "attributes": ["selected"] }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "icon_use_gitignore",
        "layer0.texture": "ayu/assets/gitignore.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [
            12,
            12
        ]
    },
    {
        "class": "icon_use_gitignore",
        "parents": [
        {
            "class": "icon_button_control",
            "attributes": ["selected"] }],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "sidebar_button_control",
        "layer0.texture": "ayu/assets/sidebar.png",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.opacity": 1,
        "content_margin": [
            12,
            12
        ]
    },
    {
        "class": "sidebar_button_control",
        "attributes": [
            "hover"
        ],
        "layer0.tint": "hsl(40, 75%, 60%)"
    },
    {
        "class": "label_control",
        "color": "hsl(220, 10%, 35%)",
        "shadow_color": [
            0,
            0,
            0,
            0
        ],
        "shadow_offset": [
            0,
            0
        ],
        "font.bold": false,
        "font.size": 12
    },
    //--------------------------------------------------------
    // Statusbar -> Background
    //--------------------------------------------------------
    {
        "class": "label_control",
        "parents": [
        {
            "class": "status_bar"
        }],
        "color": "hsl(220, 10%, 35%)",
        "font.bold": false
    },
    //--------------------------------------------------------
    // Statusbar -> Foreground
    //--------------------------------------------------------
    {
        "class": "label_control",
        "parents": [
        {
            "class": "button_control"
        }],
        "color": "hsl(220, 10%, 35%)"
    },
    {
        "class": "label_control",
        "parents": [
        {
            "class": "button_control",
            "attributes": ["hover"]
        }],
        "color": "hsl(40, 100%, 25%)"
    },
    {
        "class": "title_label_control",
        "color": "hsl(40, 75%, 60%)"
    },

    //--------------------------------------------------------
    // (Tooltips)
    //--------------------------------------------------------
    // Background
    {
        "class": "tool_tip_control",
        "layer0.tint": "hsl(220, 10%, 35%)",
        "layer0.inner_margin": [
            0,
            0
        ],
        "layer0.opacity": 1,
        "content_margin": [
            6,
            3
        ]
    },
    // Foreground
    {
        "class": "tool_tip_label_control",
        "color": "hsl(220, 30%, 10%)",
        "font.size": 12
    }
]
