<?xml version="1.0" encoding="UTF-8"?>
<plist version="1.0">
<dict>
	<key>scope</key>
	<string>source.dosbatch</string>
	<key>settings</key>
	<dict>
		<key>shellVariables</key>
		<array>
			<dict>
				<key>name</key>
				<string>TM_COMMENT_START</string>
				<key>value</key>
				<string>:: </string>
			</dict>
			<dict>
				<key>name</key>
				<string>TM_COMMENT_CASE_INSENSITIVE</string>
				<key>value</key>
				<string>yes</string>
			</dict>
			<dict>
				<key>name</key>
				<string>TM_COMMENT_START_2</string>
				<key>value</key>
				<string>:: </string>
			</dict>
			<dict>
				<key>name</key>
				<string>TM_COMMENT_CASE_INSENSITIVE_2</string>
				<key>value</key>
				<string>yes</string>
			</dict>
			<dict>
				<key>name</key>
				<string>TM_COMMENT_START_3</string>
				<key>value</key>
				<string>rem </string>
				<!-- <string>:: </string> -->
			</dict>
			<dict>
				<key>name</key>
				<string>TM_COMMENT_START_4</string>
				<key>value</key>
				<string>::: </string>
				<!-- <string>@rem </string> -->
			</dict>
		</array>
	</dict>
</dict>
</plist>
