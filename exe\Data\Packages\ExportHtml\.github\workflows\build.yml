name: build

on:
  push:
    branches:
      - 'master'
    tags:
    - '**'
  pull_request:
    branches:
    - '**'

jobs:
  tests:

    env:
      TOXENV: py311

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools tox
    - name: Tests
      run: |
        python -m tox

  lint:

    env:
      TOXENV: lint

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    - name: Setup Node
      uses: actions/setup-node@v1
      with:
        node-version: '14'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools tox
        npm install -g jshint
    - name: Lint
      run: |
        python -m tox
        jshint .

  documents:

    env:
      TOXENV: documents

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools tox
    - name: Install Aspell
      run: |
        sudo apt-get install aspell aspell-en
    - name: Build documents
      run: |
        python -m tox
