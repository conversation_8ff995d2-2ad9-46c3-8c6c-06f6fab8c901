template: 'facelessuser:master-labels:labels.yml:master'

# Wildcard labels

brace_expansion: true
extended_glob: true

rules:
  - labels: ['C: infrastructure']
    patterns: ['*|-@(*.md|*.py|*.sublime-@(keymap|menu|settings|commands))', '.github/**']

  - labels: ['C: source']
    patterns: ['**/@(*.py|*.sublime-@(keymap|menu|settings|commands))|-tests']

  - labels: ['C: docs']
    patterns: ['**/*.md|docs/**']

  - labels: ['C: tests']
    patterns: ['tests/**']

  - labels: ['C: plugins']
    patterns: ['as_plugins/**']

  - labels: ['C: settings']
    patterns: ['*.sublime-@(keymap|menu|settings|commands)']

# Label management

labels:
- name: 'C: settings'
  color: subcategory
  description: Related to Sublime settings.

- name: 'C: plugins'
  color: subcategory
  description: Related to plugin modules.
