[{"class": "title_bar", "settings": ["!ui_native_titlebar"], "bg": "#fcfcfc", "fg": "#5c6166"}, {"class": "title_bar", "settings": ["!ui_native_titlebar", "ui_separator"], "bg": "#f8f9fa"}, {"class": "sidebar_container", "content_margin": [0, 6, 0, 0], "layer0.opacity": 1, "layer0.tint": "#fcfcfc"}, {"class": "sidebar_container", "settings": ["ui_separator"], "layer0.tint": "#f8f9fa", "layer1.texture": "ayu/assets/separator-sidebar.png", "layer1.inner_margin": [0, 38, 2, 1], "layer1.opacity": 1, "layer1.tint": "#6b7d8f1a"}, {"class": "sidebar_tree", "indent_top_level": false, "row_padding": [20, 5], "dark_content": false, "spacer_rows": true, "indent_offset": 2, "indent": 8}, {"class": "sidebar_heading", "color": "#8a9199", "font.bold": true, "font.size": 11}, {"class": "tree_row", "layer0.texture": "ayu/assets/tree-highlight.png", "layer0.tint": "#56728f00", "layer0.inner_margin": [8, 4], "layer0.opacity": 1, "layer1.texture": "ayu/assets/tree-highlight-border.png", "layer1.tint": "#56728f1f", "layer1.inner_margin": [8, 4], "layer1.opacity": 0}, {"class": "tree_row", "attributes": ["selectable", "hover"], "layer0.tint": "#56728f1f", "layer1.opacity": 1}, {"class": "tree_row", "attributes": ["selectable", "selected"], "layer0.tint": "#6b7d8f1f"}, {"class": "tree_row", "attributes": ["selectable", "selected", "hover"], "layer0.tint": "#56728f1f"}, {"class": "sidebar_label", "fg": "#8a9199", "font.size": 12}, {"class": "sidebar_label", "parents": [{"class": "tree_row", "attributes": ["hover"]}], "fg": "#5c6166"}, {"class": "sidebar_label", "parents": [{"class": "tree_row", "attributes": ["selected"]}], "fg": "#5c6166"}, {"class": "sidebar_label", "parents": [{"class": "tree_row", "attributes": ["expandable"]}], "fg": "#8a9199", "font.bold": false}, {"class": "sidebar_label", "parents": [{"class": "tree_row", "attributes": ["expandable"]}], "settings": ["bold_folder_labels"], "font.bold": true}, {"class": "sidebar_label", "parents": [{"class": "tree_row", "attributes": ["expandable", "hover"]}], "fg": "#5c6166"}, {"class": "sidebar_label", "parents": [{"class": "tree_row", "attributes": ["expanded"]}], "fg": "#5c6166"}, {"class": "sidebar_label", "parents": [{"class": "tree_row", "attributes": ["expanded"]}], "settings": ["bold_folder_labels"], "font.bold": true}, {"class": "sidebar_label", "attributes": ["transient"], "font.italic": false}, {"class": "sidebar_label", "parents": [{"class": "file_system_entry", "attributes": ["ignored"]}], "fg": "#8a919980"}, {"class": "disclosure_button_control", "content_margin": [0, 0, 0, 0]}, {"class": "close_button", "content_margin": [6, 8], "layer0.texture": "ayu/assets/close.png", "layer0.opacity": 0, "layer0.inner_margin": [0, 0], "layer0.tint": "#8a9199"}, {"class": "close_button", "parents": [{"class": "tree_row", "attributes": ["hover"]}], "layer0.opacity": 1}, {"class": "close_button", "attributes": ["dirty"], "layer0.texture": "ayu/assets/dirty.png", "layer0.tint": "#8a9199", "layer0.opacity": 1}, {"class": "close_button", "attributes": ["hover"], "layer0.opacity": 1, "layer0.tint": "#ffaa33"}, {"class": "icon_folder", "content_margin": [9, 9], "layer0.tint": "#f8f9fa", "layer0.opacity": 0, "layer1.texture": "ayu/assets/folder.png", "layer1.tint": "#8a9199bf", "layer1.opacity": 1, "layer2.texture": "ayu/assets/folder-open.png", "layer2.tint": "#ffaa33", "layer2.opacity": 0}, {"class": "icon_folder", "parents": [{"class": "tree_row", "attributes": ["expanded"]}], "layer1.opacity": 0, "layer2.opacity": 1}, {"class": "icon_folder", "parents": [{"class": "tree_row", "attributes": ["hover"]}], "layer1.tint": "#ffaa33"}, {"class": "icon_folder", "parents": [{"class": "tree_row", "attributes": ["expanded", "hover"]}], "layer2.texture": {"keyframes": ["ayu/assets/folder-open-1.png", "ayu/assets/folder-open-1.png", "ayu/assets/folder-open-2.png", "ayu/assets/folder-open-3.png", "ayu/assets/folder-open-4.png", "ayu/assets/folder-open-5.png", "ayu/assets/folder-open-5.png", "ayu/assets/folder-open-5.png", "ayu/assets/folder-open-6.png", "ayu/assets/folder-open-6.png", "ayu/assets/folder-open-6.png", "ayu/assets/folder-open-6.png", "ayu/assets/folder-open.png"], "loop": false, "frame_time": 0.02}, "layer1.opacity": 0, "layer2.opacity": 1}, {"class": "icon_folder", "parents": [{"class": "tree_row", "attributes": ["selected"]}], "layer1.tint": "#ffaa33"}, {"class": "icon_folder_loading", "layer1.texture": {"keyframes": ["ayu/assets/spinner11.png", "ayu/assets/spinner10.png", "ayu/assets/spinner9.png", "ayu/assets/spinner8.png", "ayu/assets/spinner7.png", "ayu/assets/spinner6.png", "ayu/assets/spinner5.png", "ayu/assets/spinner4.png", "ayu/assets/spinner3.png", "ayu/assets/spinner2.png", "ayu/assets/spinner1.png", "ayu/assets/spinner.png"], "loop": true, "frame_time": 0.075}, "layer1.tint": "#ffaa33", "layer0.opacity": 0, "content_margin": [8, 8]}, {"class": "icon_folder_dup", "content_margin": [9, 9], "layer0.texture": "ayu/assets/folder.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "layer1.texture": "ayu/assets/folder-symlink.png", "layer1.tint": "#8a9199", "layer1.opacity": 0.3}, {"class": "icon_folder_dup", "parents": [{"class": "tree_row", "attributes": ["hover"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_folder_dup", "parents": [{"class": "tree_row", "attributes": ["expanded"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_file_type", "content_margin": [8, 8]}, {"class": "vcs_status_badge", "attributes": ["ignored"], "layer0.tint": "#8a91994d"}, {"class": "vcs_status_badge", "attributes": ["added"], "layer0.tint": "#6cbf4366"}, {"class": "vcs_status_badge", "attributes": ["modified"], "layer0.tint": "#478acc66"}, {"class": "vcs_status_badge", "attributes": ["deleted"], "layer0.tint": "#ff738366"}, {"class": "sheet_contents", "background_modifier": ""}, {"class": "sheet_contents", "settings": {"inactive_sheet_dimming": true}, "attributes": ["!highlighted"], "background_modifier": "blend(#f8f9fa 0%)"}, {"class": "tabset_control", "mouse_wheel_switch": false, "tab_min_width": 50, "tab_overlap": 0, "tab_height": 38, "tab_width": 100, "layer0.tint": "#fcfcfc", "layer0.opacity": 1, "content_margin": [10, 0]}, {"class": "tabset_control", "settings": ["mouse_wheel_switches_tabs", "!enable_tab_scrolling"], "mouse_wheel_switch": true}, {"class": "tabset_control", "settings": ["ui_separator"], "tab_overlap": 8, "connector_height": 2, "content_margin": [0, 0, 0, 0], "layer0.tint": "#f8f9fa", "layer1.texture": "ayu/assets/tabset-border.png", "layer1.tint": "#6b7d8f1a", "layer1.inner_margin": [1, 1, 1, 6], "layer1.opacity": 1}, {"class": "tab_connector", "layer0.texture": "", "layer0.opacity": 1, "tint_index": 0}, {"class": "tab_control", "settings": ["!ui_separator"], "layer0.texture": "ayu/assets/separator-bottom.png", "layer0.tint": "#6b7d8f1a", "layer0.inner_margin": [0, 0, 0, 2], "layer0.opacity": 0, "content_margin": [15, -2, 15, 0], "max_margin_trim": 12}, {"class": "tab_control", "settings": ["ui_separator"], "layer1.texture": "ayu/assets/tab.png", "layer1.inner_margin": [9, 0, 9, 0], "layer1.opacity": 0, "layer2.texture": "ayu/assets/tab-border.png", "layer2.inner_margin": [9, 0, 9, 0], "layer2.tint": "#6b7d8f1a", "layer2.opacity": 0, "content_margin": [16, 5, 11, 4], "hit_test_level": 0}, {"class": "tab_control", "attributes": ["selected"], "settings": ["!ui_separator"], "layer0.tint": "#ffaa33", "layer0.opacity": 1}, {"class": "tab_control", "attributes": ["selected", "!highlighted"], "settings": ["ui_separator"], "layer1.opacity": 1, "layer1.tint": "#f8f9fa", "layer2.opacity": 1}, {"class": "tab_control", "attributes": ["selected", "highlighted"], "settings": ["ui_separator"], "layer1.opacity": {"target": 1, "speed": 1, "interpolation": "smoothstep"}, "layer1.tint": "#fcfcfc", "layer2.opacity": 1}, {"class": "tab_control", "attributes": ["hover"], "settings": ["!ui_separator"], "layer0.tint": "#ffaa33", "layer0.opacity": 1}, {"class": "tab_control", "attributes": ["hover"], "settings": ["ui_separator"]}, {"class": "tab_control", "attributes": ["selected", "hover"], "settings": ["!ui_separator"], "layer0.opacity": 1}, {"class": "tab_control", "attributes": ["selected", "hover"], "settings": ["ui_separator"]}, {"class": "tab_label", "fg": "#8a9199", "font.italic": false, "font.bold": false, "font.size": 12}, {"class": "tab_label", "settings": ["highlight_modified_tabs"], "font.italic": true, "attributes": ["dirty"], "fg": "#ffaa33"}, {"class": "tab_label", "parents": [{"class": "tab_control", "attributes": ["selected", "highlighted"]}], "fg": "#5c6166"}, {"class": "tab_label", "parents": [{"class": "tab_control", "attributes": ["hover"]}], "fg": "#5c6166"}, {"class": "tab_label", "attributes": ["transient"], "font.italic": true}, {"class": "tab_close_button", "content_margin": [0, 0], "layer0.texture": "ayu/assets/close.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "layer1.texture": "ayu/assets/dirty.png", "layer1.tint": "#8a9199", "layer1.opacity": 0}, {"class": "tab_close_button", "settings": ["show_tab_close_buttons"], "content_margin": [6, 8]}, {"class": "tab_close_button", "settings": ["show_tab_close_buttons", "highlight_modified_tabs"], "attributes": ["hover"], "layer0.tint": "#ffaa33"}, {"class": "tab_close_button", "parents": [{"class": "tab_control", "attributes": ["dirty"]}], "layer0.opacity": 0, "layer1.opacity": 1, "content_margin": [6, 8]}, {"class": "tab_close_button", "parents": [{"class": "tab_control", "attributes": ["dirty"]}], "attributes": ["hover"], "layer0.opacity": 1, "layer1.opacity": 0}, {"class": "tab_close_button", "parents": [{"class": "tab_control", "attributes": ["selected", "dirty"]}], "layer0.opacity": 0, "layer1.opacity": 1, "layer1.tint": "#ffaa33"}, {"class": "tab_close_button", "parents": [{"class": "tab_control", "attributes": ["selected", "dirty"]}], "attributes": ["hover"], "layer0.opacity": 1, "layer1.opacity": 0}, {"class": "scroll_tabs_left_button", "content_margin": [12, 15], "layer0.texture": "ayu/assets/arrow-left.png", "layer0.tint": "#8a9199", "layer0.opacity": 1}, {"class": "scroll_tabs_left_button", "attributes": ["hover"], "layer0.tint": "#ffaa33"}, {"class": "scroll_tabs_right_button", "content_margin": [12, 15], "layer0.texture": "ayu/assets/arrow-right.png", "layer0.tint": "#8a9199", "layer0.opacity": 1}, {"class": "scroll_tabs_right_button", "attributes": ["hover"], "layer0.tint": "#ffaa33"}, {"class": "show_tabs_dropdown_button", "content_margin": [12, 12], "layer0.texture": "ayu/assets/overflow-menu.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "layer0.inner_margin": [0, 0]}, {"class": "show_tabs_dropdown_button", "attributes": ["hover"], "layer0.tint": "#ffaa33"}, {"class": "overlay_control", "layer0.texture": "ayu/assets/overlay-shadow.png", "layer0.inner_margin": [15, 35, 15, 25], "layer0.opacity": 1, "layer0.tint": "#00000014", "layer1.texture": "ayu/assets/overlay-border.png", "layer1.inner_margin": [15, 35, 15, 25], "layer1.opacity": 1, "layer1.tint": "#6b7d8f1a", "layer2.texture": "ayu/assets/overlay-bg.png", "layer2.inner_margin": [15, 35, 15, 25], "layer2.opacity": 1, "layer2.tint": "#ffffff", "content_margin": [10, 35, 10, 20]}, {"class": "quick_panel", "row_padding": [13, 7], "layer0.tint": "#ffffff", "layer0.opacity": 1}, {"class": "quick_panel", "parents": [{"class": "overlay_control"}], "row_padding": [13, 7], "layer0.tint": "#ffffff", "layer0.opacity": 1}, {"class": "mini_quick_panel_row", "layer0.texture": "ayu/assets/tree-highlight.png", "layer0.tint": "#56728f1f", "layer0.inner_margin": [8, 4], "layer0.opacity": 0, "layer1.texture": "ayu/assets/tree-highlight-border.png", "layer1.tint": "#56728f1f", "layer1.inner_margin": [8, 4], "layer1.opacity": 0}, {"class": "mini_quick_panel_row", "attributes": ["selected"], "layer0.opacity": 1, "layer1.opacity": 1}, {"class": "quick_panel_row", "layer0.texture": "ayu/assets/tree-highlight.png", "layer0.tint": "#56728f1f", "layer0.inner_margin": [8, 4], "layer0.opacity": 0, "layer1.texture": "ayu/assets/tree-highlight-border.png", "layer1.tint": "#56728f1f", "layer1.inner_margin": [8, 4], "layer1.opacity": 0}, {"class": "quick_panel_row", "attributes": ["selected"], "layer0.opacity": 1, "layer1.opacity": 1}, {"class": "quick_panel_label", "fg": "#8a9199", "match_fg": "#ffaa33", "selected_fg": "#5c6166", "selected_match_fg": "#ffaa33"}, {"class": "quick_panel_label", "parents": [{"class": "overlay_control"}], "fg": "#8a9199", "match_fg": "#ffaa33", "selected_fg": "#5c6166", "selected_match_fg": "#ffaa33"}, {"class": "quick_panel_path_label", "fg": "#8a9199", "match_fg": "#5c6166", "selected_fg": "#8a9199", "selected_match_fg": "#5c6166"}, {"class": "quick_panel_detail_label", "link_color": "#22a4e6"}, {"class": "grid_layout_control", "border_size": 0, "border_color": "#eaedef"}, {"class": "grid_layout_control", "settings": ["ui_separator"], "border_size": 0}, {"class": "minimap_control", "settings": ["always_show_minimap_viewport"], "viewport_color": "#8a9199", "viewport_opacity": 0.3}, {"class": "minimap_control", "settings": ["!always_show_minimap_viewport"], "viewport_color": "#8a9199", "viewport_opacity": {"target": 0, "speed": 4, "interpolation": "smoothstep"}}, {"class": "minimap_control", "attributes": ["hover"], "settings": ["!always_show_minimap_viewport"], "viewport_opacity": {"target": 0.3, "speed": 4, "interpolation": "smoothstep"}}, {"class": "fold_button_control", "layer0.texture": "ayu/assets/unfold.png", "layer0.opacity": 1, "layer0.inner_margin": 0, "layer0.tint": "#8a9199", "content_margin": [8, 6, 8, 6]}, {"class": "fold_button_control", "attributes": ["hover"], "layer0.tint": "#ffaa33"}, {"class": "fold_button_control", "attributes": ["expanded"], "layer0.texture": "ayu/assets/fold.png"}, {"class": "popup_shadow", "layer0.texture": "ayu/assets/popup-shadow.png", "layer0.inner_margin": [14, 11, 14, 15], "layer0.opacity": 1, "layer0.tint": "#00000014", "layer0.draw_center": false, "layer1.texture": "ayu/assets/popup-border.png", "layer1.inner_margin": [14, 11, 14, 15], "layer1.opacity": 1, "layer1.tint": "#6b7d8f1a", "layer1.draw_center": false, "content_margin": [10, 7, 10, 13]}, {"class": "popup_control", "layer0.texture": "ayu/assets/popup-bg.png", "layer0.inner_margin": [4, 4, 4, 4], "layer0.opacity": 1, "layer0.tint": "#ffffff", "content_margin": [0, 4]}, {"class": "auto_complete", "row_padding": [5, 0]}, {"class": "table_row", "layer0.texture": "ayu/assets/tree-highlight.png", "layer0.tint": "#56728f1f", "layer0.inner_margin": [8, 4], "layer0.opacity": 0, "layer1.texture": "ayu/assets/tree-highlight-border.png", "layer1.tint": "#56728f1f", "layer1.inner_margin": [8, 4], "layer1.opacity": 0}, {"class": "table_row", "attributes": ["selected"], "layer0.opacity": 1, "layer1.opacity": 1}, {"class": "auto_complete_label", "fg": "transparent", "match_fg": "#ffaa33", "selected_fg": "transparent", "selected_match_fg": "#ffaa33", "fg_blend": true}, {"class": "auto_complete_hint", "opacity": 0.7, "font.italic": true}, {"class": "kind_container", "layer0.texture": "ayu/assets/kind-bg.png", "layer0.tint": "#ffffff", "layer0.inner_margin": [4, 4, 7, 4], "layer0.opacity": 0, "layer1.texture": "ayu/assets/kind-bg.png", "layer1.tint": "#ffffff00", "layer1.inner_margin": [4, 4, 7, 4], "layer1.opacity": 0.3, "layer2.texture": "ayu/assets/kind-border.png", "layer2.tint": "#ffffff00", "layer2.inner_margin": [4, 4, 7, 4], "layer2.opacity": 0.1, "content_margin": [4, 0, 6, 0]}, {"class": "kind_label", "font.size": "1rem", "font.bold": true, "font.italic": true, "color": "#8a9199"}, {"class": "kind_label", "parents": [{"class": "quick_panel"}], "font.size": "1.1rem"}, {"class": "kind_container kind_function", "layer0.opacity": 1, "layer1.tint": "#f2ae49", "layer2.tint": "#f2ae49"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_function"}], "color": "#f2ae49"}, {"class": "kind_container kind_keyword", "layer0.opacity": 1, "layer1.tint": "#fa8d3e", "layer2.tint": "#fa8d3e"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_keyword"}], "color": "#fa8d3e"}, {"class": "kind_container kind_markup", "layer0.opacity": 1, "layer1.tint": "#55b4d4", "layer2.tint": "#55b4d4"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_markup"}], "color": "#55b4d4"}, {"class": "kind_container kind_namespace", "layer0.opacity": 1, "layer1.tint": "#22a4e6", "layer2.tint": "#22a4e6"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_namespace"}], "color": "#22a4e6"}, {"class": "kind_container kind_navigation", "layer0.opacity": 1, "layer1.tint": "#e6b673", "layer2.tint": "#e6b673"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_navigation"}], "color": "#e6b673"}, {"class": "kind_container kind_snippet", "layer0.opacity": 1, "layer1.tint": "#f07171", "layer2.tint": "#f07171"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_snippet"}], "color": "#f07171"}, {"class": "kind_container kind_type", "layer0.opacity": 1, "layer1.tint": "#22a4e6", "layer2.tint": "#22a4e6"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_type"}], "color": "#22a4e6"}, {"class": "kind_container kind_variable", "layer0.opacity": 1, "layer1.tint": "#787b8099", "layer2.tint": "#787b8099"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_variable"}], "color": "#787b8099"}, {"class": "kind_container kind_color_redish", "layer0.opacity": 1, "layer1.tint": "#f07171", "layer2.tint": "#f07171"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_color_redish"}], "color": "#f07171"}, {"class": "kind_container kind_color_orangish", "layer0.opacity": 1, "layer1.tint": "#fa8d3e", "layer2.tint": "#fa8d3e"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_color_orangish"}], "color": "#fa8d3e"}, {"class": "kind_container kind_color_yellowish", "layer0.opacity": 1, "layer1.tint": "#f2ae49", "layer2.tint": "#f2ae49"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_color_yellowish"}], "color": "#f2ae49"}, {"class": "kind_container kind_color_greenish", "layer0.opacity": 1, "layer1.tint": "#86b300", "layer2.tint": "#86b300"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_color_greenish"}], "color": "#86b300"}, {"class": "kind_container kind_color_cyanish", "layer0.opacity": 1, "layer1.tint": "#4cbf99", "layer2.tint": "#4cbf99"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_color_cyanish"}], "color": "#4cbf99"}, {"class": "kind_container kind_color_bluish", "layer0.opacity": 1, "layer1.tint": "#55b4d4", "layer2.tint": "#55b4d4"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_color_bluish"}], "color": "#55b4d4"}, {"class": "kind_container kind_color_purplish", "layer0.opacity": 1, "layer1.tint": "#a37acc", "layer2.tint": "#a37acc"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_color_purplish"}], "color": "#a37acc"}, {"class": "kind_container kind_color_pinkish", "layer0.opacity": 1, "layer1.tint": "#ed9366", "layer2.tint": "#ed9366"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_color_pinkish"}], "color": "#ed9366"}, {"class": "kind_container kind_color_dark", "layer0.opacity": 1, "layer1.tint": "#8a9199", "layer2.tint": "#8a9199"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_color_dark"}], "color": "#8a9199"}, {"class": "kind_container kind_color_light", "layer0.opacity": 1, "layer1.tint": "white", "layer2.tint": "white"}, {"class": "kind_label", "parents": [{"class": "kind_container kind_color_light"}], "color": "#555"}, {"class": "symbol_container", "content_margin": [4, 3, 4, 3]}, {"class": "trigger_container", "content_margin": [4, 3, 4, 3]}, {"class": "auto_complete_detail_pane", "layer0.opacity": 1, "layer0.tint": "#ffffff", "layer1.opacity": 1, "layer1.tint": "#ffffff", "content_margin": [8, 10, 8, 5]}, {"class": "auto_complete_kind_name_label", "font.size": "0.9rem", "font.italic": true, "border_color": "#8a9199"}, {"class": "auto_complete_details", "background_color": "#ffffff", "monospace_background_color": "#ffffff"}, {"class": "panel_control", "layer0.tint": "#fcfcfc", "layer0.opacity": 1, "content_margin": [0, 5]}, {"class": "panel_control", "settings": ["ui_separator"], "layer0.tint": "#f8f9fa", "layer1.texture": "ayu/assets/separator-top.png", "layer1.tint": "#6b7d8f1a", "layer1.inner_margin": [1, 2, 1, 0], "layer1.opacity": 1}, {"class": "panel_grid_control"}, {"class": "panel_close_button", "layer0.texture": "ayu/assets/close.png", "layer0.opacity": 1, "layer0.tint": "#8a9199", "content_margin": [0, 0]}, {"class": "panel_close_button", "attributes": ["hover"], "layer0.tint": "#ffaa33"}, {"class": "status_bar", "layer0.texture": "", "layer0.tint": "#fcfcfc", "layer0.opacity": 1, "layer1.texture": "ayu/assets/separator-top.png", "layer1.tint": "#6b7d8f1a", "layer1.inner_margin": [1, 2, 1, 0], "content_margin": [10, 2]}, {"class": "status_bar", "settings": ["ui_separator"], "layer0.tint": "#f8f9fa", "layer1.opacity": 1}, {"class": "panel_button_control", "layer0.texture": "ayu/assets/switch-panel.png", "layer0.tint": "#8a9199", "layer0.opacity": 1}, {"class": "panel_button_control", "attributes": ["hover"], "layer0.tint": "#ffaa33"}, {"class": "status_container", "content_margin": [0, 5]}, {"class": "status_button", "min_size": [100, 0]}, {"class": "vcs_branch_icon", "layer0.tint": "#8a9199"}, {"class": "vcs_changes_annotation", "border_color": "#8a9199b3"}, {"class": "dialog", "layer0.tint": "#f8f9fa", "layer0.opacity": 1}, {"class": "progress_bar_control", "layer0.tint": "#f8f9fa", "layer0.opacity": 1}, {"class": "progress_gauge_control", "layer0.tint": "#ffaa33", "layer0.opacity": 1, "content_margin": [0, 6]}, {"class": "scroll_area_control", "settings": ["overlay_scroll_bars"], "overlay": true}, {"class": "scroll_area_control", "settings": ["!overlay_scroll_bars"], "overlay": false}, {"class": "scroll_bar_control", "layer0.tint": "#f8f9fa", "layer0.opacity": 1, "layer1.texture": "ayu/assets/scrollbar-vertical-wide.png", "layer1.tint": "#8a9199", "layer1.opacity": 0.1, "layer1.inner_margin": [0, 10]}, {"class": "scroll_bar_control", "parents": [{"class": "overlay_control"}], "layer0.tint": "#ffffff"}, {"class": "scroll_bar_control", "attributes": ["horizontal"], "layer1.texture": "ayu/assets/scrollbar-horizontal-wide.png", "layer1.inner_margin": [10, 0]}, {"class": "scroll_bar_control", "settings": ["overlay_scroll_bars"], "layer0.opacity": 0, "layer1.texture": "ayu/assets/scrollbar-vertical.png", "layer1.inner_margin": [4, 6, 6, 6]}, {"class": "scroll_bar_control", "settings": ["overlay_scroll_bars", "ui_wide_scrollbars"], "layer0.texture": "ayu/assets/scrollbar-vertical-wide.png"}, {"class": "scroll_bar_control", "settings": ["overlay_scroll_bars"], "attributes": ["horizontal"], "layer0.opacity": 0, "layer1.texture": "ayu/assets/scrollbar-horizontal.png", "layer1.inner_margin": [6, 4, 6, 6]}, {"class": "scroll_bar_control", "attributes": ["horizontal"], "settings": ["overlay_scroll_bars", "ui_wide_scrollbars"], "layer0.texture": "ayu/assets/scrollbar-horizontal-wide.png"}, {"class": "scroll_track_control", "layer0.tint": "#f8f9fa", "layer0.opacity": 1}, {"class": "scroll_corner_control", "layer0.tint": "#f8f9fa", "layer0.opacity": 1}, {"class": "puck_control", "layer0.texture": "ayu/assets/scrollbar-vertical-wide.png", "layer0.tint": "#8a9199", "layer0.opacity": 0.3, "layer0.inner_margin": [0, 10], "content_margin": [6, 12]}, {"class": "puck_control", "attributes": ["horizontal"], "layer0.texture": "ayu/assets/scrollbar-horizontal-wide.png", "layer0.inner_margin": [10, 0], "content_margin": [12, 6]}, {"class": "puck_control", "settings": ["overlay_scroll_bars"], "layer0.texture": "ayu/assets/scrollbar-vertical.png", "layer0.inner_margin": [4, 6, 6, 6], "content_margin": [5, 20]}, {"class": "puck_control", "settings": ["overlay_scroll_bars", "ui_wide_scrollbars"], "layer0.texture": "ayu/assets/scrollbar-vertical-wide.png"}, {"class": "puck_control", "settings": ["overlay_scroll_bars"], "attributes": ["horizontal"], "layer0.texture": "ayu/assets/scrollbar-horizontal.png", "layer0.inner_margin": [6, 4, 6, 6], "content_margin": [20, 5]}, {"class": "puck_control", "attributes": ["horizontal"], "settings": ["overlay_scroll_bars", "ui_wide_scrollbars"], "layer0.texture": "ayu/assets/scrollbar-horizontal-wide.png"}, {"class": "text_line_control", "layer0.texture": "ayu/assets/input-bg.png", "layer0.opacity": 1, "layer0.inner_margin": [10, 8], "layer0.tint": "#ffffff", "layer1.texture": "ayu/assets/input-border.png", "layer1.opacity": 1, "layer1.inner_margin": [10, 8], "layer1.tint": "#6b7d8f1a", "content_margin": [10, 7, 10, 5]}, {"class": "text_line_control", "parents": [{"class": "overlay_control"}], "layer0.texture": "", "layer0.opacity": 0, "layer1.texture": "ayu/assets/input-prompt.png", "layer1.opacity": 1, "layer1.tint": "#8a9199", "layer1.inner_margin": [36, 26, 0, 0], "content_margin": [38, 5, 10, 5]}, {"class": "text_line_control", "parents": [{"class": "overlay_control goto_file"}], "layer1.texture": "ayu/assets/input-search.png"}, {"class": "text_line_control", "parents": [{"class": "overlay_control command_palette"}], "layer1.texture": "ayu/assets/input-command.png"}, {"class": "text_line_control", "parents": [{"class": "overlay_control goto_symbol"}], "layer1.texture": "ayu/assets/input-symbol.png"}, {"class": "text_line_control", "parents": [{"class": "overlay_control goto_symbol_in_project"}], "layer1.texture": "ayu/assets/input-symbol.png"}, {"class": "text_line_control", "parents": [{"class": "overlay_control goto_word"}], "layer1.texture": "ayu/assets/input-word.png"}, {"class": "dropdown_button_control", "content_margin": [12, 12], "layer0.texture": "ayu/assets/overflow-menu.png", "layer0.tint": "#8a9199", "layer0.opacity": 1}, {"class": "dropdown_button_control", "attributes": ["hover"], "layer0.tint": "#ffaa33"}, {"class": "button_control", "content_margin": [15, 9, 15, 10], "min_size": [60, 0], "layer0.tint": "#ffaa33", "layer0.texture": "ayu/assets/input-bg.png", "layer0.inner_margin": [10, 8], "layer0.opacity": 0}, {"class": "button_control", "attributes": ["hover"], "layer0.opacity": 1}, {"class": "icon_button_control", "layer0.tint": [0, 0, 0], "layer0.opacity": 0, "layer2.tint": "#5c6166", "layer2.opacity": {"target": 0, "speed": 10, "interpolation": "smoothstep"}, "content_margin": [10, 5]}, {"class": "icon_regex", "layer0.texture": "ayu/assets/regex.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "icon_regex", "parents": [{"class": "icon_button_control", "attributes": ["selected"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_case", "layer0.texture": "ayu/assets/matchcase.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "icon_case", "parents": [{"class": "icon_button_control", "attributes": ["selected"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_whole_word", "layer0.texture": "ayu/assets/word.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "icon_whole_word", "parents": [{"class": "icon_button_control", "attributes": ["selected"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_wrap", "layer0.texture": "ayu/assets/wrap.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "icon_wrap", "parents": [{"class": "icon_button_control", "attributes": ["selected"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_in_selection", "layer0.texture": "ayu/assets/inselection.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "icon_in_selection", "parents": [{"class": "icon_button_control", "attributes": ["selected"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_highlight", "layer0.texture": "ayu/assets/highlight.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "icon_highlight", "parents": [{"class": "icon_button_control", "attributes": ["selected"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_preserve_case", "layer0.texture": "ayu/assets/replace-preserve-case.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "icon_preserve_case", "parents": [{"class": "icon_button_control", "attributes": ["selected"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_context", "layer0.texture": "ayu/assets/context.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "icon_context", "parents": [{"class": "icon_button_control", "attributes": ["selected"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_use_buffer", "layer0.texture": "ayu/assets/buffer.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "icon_use_buffer", "parents": [{"class": "icon_button_control", "attributes": ["selected"]}], "layer0.tint": "#ffaa33"}, {"class": "icon_use_gitignore", "layer0.texture": "ayu/assets/gitignore.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "icon_use_gitignore", "parents": [{"class": "icon_button_control", "attributes": ["selected"]}], "layer0.tint": "#ffaa33"}, {"class": "sidebar_button_control", "layer0.texture": "ayu/assets/sidebar.png", "layer0.tint": "#8a9199", "layer0.opacity": 1, "content_margin": [12, 12]}, {"class": "sidebar_button_control", "attributes": ["hover"], "layer0.tint": "#ffaa33"}, {"class": "label_control", "color": "#8a9199", "shadow_color": [0, 0, 0, 0], "shadow_offset": [0, 0], "font.bold": false, "font.size": 12}, {"class": "label_control", "parents": [{"class": "status_bar"}], "color": "#8a9199", "font.bold": false}, {"class": "label_control", "parents": [{"class": "button_control"}], "color": "#8a9199"}, {"class": "label_control", "parents": [{"class": "button_control", "attributes": ["hover"]}], "color": "#804a00"}, {"class": "title_label_control", "color": "#ffaa33"}, {"class": "tool_tip_control", "layer0.tint": "#8a9199", "layer0.inner_margin": [0, 0], "layer0.opacity": 1, "content_margin": [6, 3]}, {"class": "tool_tip_label_control", "color": "#f8f9fa", "font.size": 12}]