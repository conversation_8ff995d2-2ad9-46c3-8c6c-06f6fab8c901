<snippet>
    <!-- [INSERTED CODE] -->
    <content><![CDATA[
:: Exit with message (auto-close after specified amount of seconds)
SET "SecondsUntilExit=${1:10}"
ECHO.
ECHO.
ECHO Window will close automatically in %SecondsUntilExit% seconds ...
PING 127.0.0.1 -n %SecondsUntilExit% > NUL
EXIT
]]></content>
    <!-- Optional: Tab trigger to activate the snippet -->
    <tabTrigger>ex</tabTrigger>
    <!-- Optional: Scope the tab trigger will be active in -->
    <scope>source.dosbatch</scope>
    <!-- Optional: Description to show in the menu -->
    <description>(Custom) Exit with message (variable)</description>
</snippet>

