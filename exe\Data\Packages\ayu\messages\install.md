**ayu theme for Sublime Text**
To learn more about ayu visit https://github.com/dempfi/ayu
===============================================================================

Activate ayu

Open command palette via `Tools > Command Palette` (or CMD/CTRL + SHIFT + p)
and type `ayu: Activate theme`.

                                  ===

You can also manually activate this theme by adding these lines to your user
settings (Preferences > Settings - User):

Light:
```json
"theme": "ayu-light.sublime-theme",
"color_scheme": "Packages/ayu/ayu-light.sublime-color-scheme",
```

Mirage:
```json
"theme": "ayu-mirage.sublime-theme",
"color_scheme": "Packages/ayu/ayu-mirage.sublime-color-scheme",
```

Dark:
```json
"theme": "ayu-dark.sublime-theme",
"color_scheme": "Packages/ayu/ayu-dark.sublime-color-scheme",
```

================================================================================

Ayu provides following options to customize the theme

```json
"ui_native_titlebar":       true, // native titlebar on macOs
"ui_separator":             true, // separators between panels
"ui_wide_scrollbars":       true, // wider scrollbars
```
