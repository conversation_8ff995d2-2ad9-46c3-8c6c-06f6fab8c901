<snippet>
    <!-- [INSERTED CODE] -->
    <content><![CDATA[
:: -----------------------------------------------------------------------------------------------------------------------------
:: USEFUL VARIABLES
:: -----------------------------------------------------------------------------------------------------------------------------
:: C:\Users\<USER>\Users\Username:
ECHO %HOMEPATH%
:: C:\ProgramData:
ECHO %ALLUSERSPROFILE%
:: Username:
ECHO %USERNAME%
:: ComputerName:
ECHO %COMPUTERNAME%
:: C:\Windows:
ECHO %WINDIR%
:: C:\Windows:
ECHO %SYSTEMROOT%
:: Displays all Environment Variables in PATH:
ECHO %PATH%
:: Displays the partition Windows is installed on (system partition):
ECHO %SYSTEMDRIVE%
:: C:\Users\<USER>\AppData\Local\Temp:
ECHO %TEMP%
:: C:\Users\<USER>\AppData\Local\Temp:
ECHO %TMP%
:: C:\Program Files:
ECHO %PROGRAMFILES%
:: C:\Users\<USER>\AppData\Roaming:
ECHO %APPDATA%
:: Windows_NT (Windows platform):
ECHO %OS%
:: Displays the domain the computer is a member of:
ECHO %USERDOMAIN%
:: Displays a random number between 0 - 32767:
ECHO %RANDOM%
:: Displays executable filetypes:
ECHO %PATHEXT%
]]></content>
    <!-- Optional: Tab trigger to activate the snippet -->
    <tabTrigger>list</tabTrigger>
    <!-- Optional: Scope the tab trigger will be active in -->
    <scope>source.dosbatch</scope>
    <!-- Optional: Description to show in the menu -->
    <description>(Custom) List useful variables</description>
</snippet>

