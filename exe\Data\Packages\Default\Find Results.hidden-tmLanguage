<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>name</key>
	<string>Find Results</string>

	<key>patterns</key>
	<array>

		<dict>
			<key>match</key>
			<string>^([^ ].*:\n)$</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>entity.name.filename.find-in-files</string>
				</dict>
			</dict>
		</dict>

		<dict>
			<key>match</key>
			<string>^( +[0-9]+) </string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.line-number.find-in-files</string>
				</dict>
			</dict>
		</dict>

		<dict>
			<key>match</key>
			<string>^( +[0-9]+)(:)(.+\n)$</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.line-number.match.find-in-files</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.line-number.match.find-in-files</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>match.find-in-files</string>
				</dict>
			</dict>
		</dict>


		<dict>
			<key>match</key>
			<string>^ +(\.{2,10}+)$</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.line-number.find-in-files</string>
				</dict>
			</dict>
		</dict>

		<dict>
			<key>match</key>
			<string>^Searching (\d+) file(?:s)? for (".+")(.+)?$</string>
			<key>name</key>
			<string>header.find-in-files</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>variable.total_files_count.find-in-files</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>string.query.find-in-files</string>
				</dict>
			</dict>
		</dict>

		<dict>
			<key>match</key>
			<string>^(\d+) match(?:es)? (?:across|in) (\d+) file(?:s)?\n</string>
			<key>name</key>
			<string>footer.find-in-files</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>variable.matched_count.find-in-files</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>variable.matched_files_count.find-in-files</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>sep.find-in-files</string>
				</dict>
			</dict>
		</dict>

		<dict>
			<key>match</key>
			<string>^(0) matches\n</string>
			<key>name</key>
			<string>footer.find-in-files</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>variable.no_matches.find-in-files</string>
				</dict>
			</dict>
		</dict>

		<dict>
			<key>match</key>
			<string>^(0) matches</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>variable.no_matches.find-in-files</string>
				</dict>
			</dict>
		</dict>

	</array>
	<key>scopeName</key>
	<string>text.find-in-files</string>
</dict>
</plist>
