name: deploy

on:
  push:
    tags:
    - 'st3-*'

jobs:

  documents:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 0
    - name: Set up Python
      uses: actions/setup-python@v1
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools
        python -m pip install -r docs/src/requirements.txt
    - name: Deploy documents
      run: |
        git config user.name facelessuser
        git config user.email "${{ secrets.GH_EMAIL }}"
        git remote add gh-token "https://${{ secrets.GH_TOKEN }}@github.com/facelessuser/ApplySyntax.git"
        git fetch gh-token && git fetch gh-token gh-pages:gh-pages
        python -m mkdocs gh-deploy -v --clean --remote-name gh-token
        git push gh-token gh-pages
