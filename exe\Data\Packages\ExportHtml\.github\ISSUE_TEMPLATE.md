Please read and fill out this template by replacing the instructions with appropriate information.  If the template is not followed, the issue will be marked `Invalid` and closed.

Before submitting an issue search past issues and read the area of the [documentation](http://facelessuser.github.io/ExportHtml/) related to your specific question, issue, or request.

---

## Description

... what is the issue / request ?

> Vague issues/requests will be marked with `Insufficient Details` for about a week.  If not corrected, they will be marked `Stale` for about a week and then closed.

> For feature requests or proposals:

> - Clearly define in as much detail as possible how you imagine the feature to work.
> - Examples are also appreciated.

> For bugs and support questions:

> - Describe the bug/question in as much detail as possible to make it clear what is wrong or what you do not > understand.
> - Provide errors from console (if available).
> - Pictures or screencasts can also be used to clarify what the issue is or what the question is.
> - Provide links to 3rd party color scheme package you are using if applicable.

## Support Info

...

> Run the following command from the menu: `Preferences->Package Settings->ExportHtml->Support Info`.  Post the result here.

## Steps to Reproduce Issue

1. First step...
2. Second step...
3. Third step...

> Provide steps to reproduce the issue. Pictures are fine, but also provide code/text I can copy and paste in order to reproduce. Omit for feature requests and feature proposals.
