{"name": "Print Color", "author": "Unknown", "variables": {"black": "#000000", "cyan": "#34a7bd", "cyan2": "#6ac4d6", "green": "#427e00", "grey": "#3b3a32", "grey2": "#a5a5a5", "grey3": "#858585", "grey4": "#2e2e2e", "orange": "#cb6500", "orange2": "#9d550f", "orange3": "#ffe792", "purple": "#7c4fcd", "red": "#c70040", "white": "#ffffff", "white2": "#e5e5e5", "white3": "#f8f8f0", "white4": "#f8f8f2", "yellow": "#8f8634"}, "globals": {"foreground": "var(black)", "background": "var(white)", "caret": "var(black)", "invisibles": "var(grey)", "line_highlight": "color(var(grey4) alpha(0.13))", "selection": "var(cyan)", "selection_foreground": "var(white)", "inactive_selection": "color(var(orange2) alpha(0.67))", "inactive_selection_foreground": "var(cyan2)", "gutter": "var(white2)", "gutter_foreground": "var(grey3)", "active_guide": "var(cyan)", "find_highlight_foreground": "var(black)", "find_highlight": "var(orange3)", "brackets_options": "underline", "brackets_foreground": "color(var(white4) alpha(0.65))", "bracket_contents_options": "underline", "bracket_contents_foreground": "color(var(white4) alpha(0.65))", "tags_options": "stippled_underline"}, "rules": [{"name": "Comment", "scope": "comment", "foreground": "var(grey2)"}, {"name": "String", "scope": "string", "foreground": "var(yellow)"}, {"name": "Number", "scope": "constant.numeric", "foreground": "var(purple)"}, {"name": "Built-in constant", "scope": "constant.language", "foreground": "var(purple)"}, {"name": "User-defined constant", "scope": "constant.character, constant.other", "foreground": "var(purple)"}, {"name": "Variable", "scope": "variable"}, {"name": "Keyword", "scope": "keyword", "foreground": "var(red)"}, {"name": "Storage", "scope": "storage", "foreground": "var(red)"}, {"name": "Storage type", "scope": "storage.type", "foreground": "var(cyan)", "font_style": "italic"}, {"name": "Class name", "scope": "entity.name.class", "foreground": "var(green)", "font_style": "underline"}, {"name": "Inherited class", "scope": "entity.other.inherited-class", "foreground": "var(green)", "font_style": "italic underline"}, {"name": "Function name", "scope": "entity.name.function", "foreground": "var(green)"}, {"name": "Function argument", "scope": "variable.parameter", "foreground": "var(orange)", "font_style": "italic"}, {"name": "Tag name", "scope": "entity.name.tag", "foreground": "var(red)"}, {"name": "Tag attribute", "scope": "entity.other.attribute-name", "foreground": "var(green)"}, {"name": "Library function", "scope": "support.function", "foreground": "var(cyan)"}, {"name": "Library constant", "scope": "support.constant", "foreground": "var(cyan)"}, {"name": "Library class/type", "scope": "support.type, support.class", "foreground": "var(cyan)", "font_style": "italic"}, {"name": "Library variable", "scope": "support.other.variable"}, {"name": "Invalid", "scope": "invalid", "foreground": "var(white3)", "background": "var(red)"}, {"name": "Invalid deprecated", "scope": "invalid.deprecated", "foreground": "var(white3)", "background": "var(purple)"}, {"name": "JSON String", "scope": "meta.structure.dictionary.json string.quoted.double.json", "foreground": "var(yellow)"}, {"name": "diff.deleted", "scope": "markup.deleted", "foreground": "var(red)"}, {"name": "diff.inserted", "scope": "markup.inserted", "foreground": "var(green)"}, {"name": "diff.changed", "scope": "markup.changed", "foreground": "var(yellow)"}]}