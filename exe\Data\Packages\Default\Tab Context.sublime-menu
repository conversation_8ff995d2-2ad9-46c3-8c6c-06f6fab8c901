// Disable the built-in context menu items when right-clicking tabs
[
	// { "command": "close_by_index", "args": { "group": -1, "index": -1 }, "caption": "Close xTab" },
	// { "command": "close_others_by_index", "args": { "group": -1, "index": -1 }, "caption": "Close Other Tabs" },
	// { "caption": "-" },
	// { "command": "close_selected", "args": {"group":-1, "index": -1}, "caption": "Close Selected Tabs" },
	// { "command": "close_unselected", "args": {"group":-1, "index": -1}, "caption": "Close Unselected Tabs" },
	// { "command": "close_to_right_by_index", "args": { "group": -1, "index": -1 }, "caption": "Close Tabs to the Right" },
	// { "command": "close_unmodified", "args": { "group": -1, "index": -1 }, "caption": "Close Unmodified Tabs" },
	// { "command": "close_unmodified_to_right_by_index", "args": { "group": -1, "index": -1 }, "caption": "Close Unmodified Tabs to the Right" },
	// { "command": "close_deleted_files", "args": { "group": -1 }, "caption": "Close Tabs With Deleted Files" },
	// { "caption": "-" },
	// { "command": "clone_file", "args": { "add_to_selection": true, "group": -1, "index": -1 }, "caption": "Split View" },
	// { "caption": "-" },
	// { "command": "new_file", "caption": "New File" },
	// { "command": "prompt_open_file", "caption": "Open File" },
]
