[{"keys": ["ctrl+alt+f", "i"], "command": "jorn_fold_commands_show_indent_blocks"}, {"keys": ["ctrl+alt+f", "r"], "command": "jorn_fold_commands_show_fold_regions"}, {"keys": ["ctrl+alt+f", "0"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 0}}, {"keys": ["ctrl+alt+f", "1"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 1}}, {"keys": ["ctrl+alt+f", "2"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 2}}, {"keys": ["ctrl+alt+f", "3"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 3}}, {"keys": ["ctrl+alt+f", "4"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 4}}, {"keys": ["ctrl+alt+f", "5"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 5}}, {"keys": ["ctrl+alt+f", "shift+1"], "command": "jorn_fold_commands_select_at_or_below_level", "args": {"level": 1}}, {"keys": ["ctrl+alt+f", "shift+2"], "command": "jorn_fold_commands_select_at_or_below_level", "args": {"level": 2}}, {"keys": ["ctrl+alt+f", "shift+3"], "command": "jorn_fold_commands_select_at_or_below_level", "args": {"level": 3}}]