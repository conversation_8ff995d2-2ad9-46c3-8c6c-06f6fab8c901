[{"keys": ["ctrl+alt+f", "h"], "command": "jorn_fold_commands_show_headings", "context": [{"key": "selector", "operator": "equal", "operand": "text.html.markdown"}]}, {"keys": ["ctrl+alt+f", "r"], "command": "jorn_fold_commands_show_fold_regions", "context": [{"key": "selector", "operator": "equal", "operand": "text.html.markdown"}]}, {"keys": ["ctrl+alt+f", "1"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 1}, "context": [{"key": "selector", "operator": "equal", "operand": "text.html.markdown"}]}, {"keys": ["ctrl+alt+f", "2"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 2}, "context": [{"key": "selector", "operator": "equal", "operand": "text.html.markdown"}]}, {"keys": ["ctrl+alt+f", "3"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 3}, "context": [{"key": "selector", "operator": "equal", "operand": "text.html.markdown"}]}, {"keys": ["ctrl+alt+f", "4"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 4}, "context": [{"key": "selector", "operator": "equal", "operand": "text.html.markdown"}]}, {"keys": ["ctrl+alt+f", "5"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 5}, "context": [{"key": "selector", "operator": "equal", "operand": "text.html.markdown"}]}, {"keys": ["ctrl+alt+f", "6"], "command": "jorn_fold_commands_select_by_level", "args": {"level": 6}, "context": [{"key": "selector", "operator": "equal", "operand": "text.html.markdown"}]}, {"keys": ["ctrl+alt+f", "shift+2"], "command": "jorn_fold_commands_select_at_or_below_level", "args": {"level": 2}, "context": [{"key": "selector", "operator": "equal", "operand": "text.html.markdown"}]}, {"keys": ["ctrl+alt+f", "shift+3"], "command": "jorn_fold_commands_select_at_or_below_level", "args": {"level": 3}, "context": [{"key": "selector", "operator": "equal", "operand": "text.html.markdown"}]}]