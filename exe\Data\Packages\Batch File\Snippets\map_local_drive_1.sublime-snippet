<snippet>
    <!-- [INSERTED CODE] -->
    <content><![CDATA[
:: Map local folder as a virtual drive
SUBST ${1:Z}: "${2:D:\\Path\\To\\Folder"}
:: Rename virtual drive label
echo Y | label ${1:Z}: ${3:DriveLabel}
:: This is to remove a local mapping
:: SUBST ${1:Z}: /D
]]></content>
    <!-- Optional: Tab trigger to activate the snippet -->
    <tabTrigger>map</tabTrigger>
    <!-- Optional: Scope the tab trigger will be active in -->
    <scope>source.dosbatch</scope>
    <!-- Optional: Description to show in the menu -->
    <description>(Custom) Local: Map Driveletter</description>
</snippet>
