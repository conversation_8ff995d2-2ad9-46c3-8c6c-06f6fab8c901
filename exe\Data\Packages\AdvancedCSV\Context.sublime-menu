[

    { "caption": "-", "id": "csv_start" },
    {
      "id": "csv",
      "caption": "CSV\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t[...]  ",
      "children": [
            // AdvancedCSV: Sort by Column
            { "caption": "-" },
            {"command": "csv_sort_by_col_asc", "caption": "Sort by column (Ascending)"},
            {"command": "csv_sort_by_col_desc", "caption": "Sort by column (Descending)"},
            // AdvancedCSV: Insert/Delete/Select Column
            {"caption": "-"},
            {"command": "csv_insert_col", "caption": "Insert column"},
            {"command": "csv_delete_col", "caption": "Delete column"},
            {"command": "csv_select_col", "caption": "Select column"},
            // AdvancedCSV: Misc
            {"caption": "-"},
            {"command": "csv_format_expand", "caption": "Justify columns"},
            {"command": "csv_format_compact", "caption": "Compact columns"},
            {"command": "csv_delete_trailing_cols", "caption": "Delete empty trailing columns"},
            {"command": "csv_set_delimiter", "caption": "Set delimiter"},
            // AdvancedCSV: Misc
            {"caption": "-"},
            {"command": "csv_evaluate", "caption": "Evaluate cells"},
            {"command": "csv_format", "caption": "Format using template"}
      ]
  }
]
