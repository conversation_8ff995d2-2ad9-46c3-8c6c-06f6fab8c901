name: build

on:
  push:
    branches:
      - 'master'
    tags:
    - '**'
  pull_request:
    branches:
    - '**'

jobs:
  tests:

    env:
      TOXENV: py38

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v1
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools tox
    - name: Tests
      run: |
        python -m tox

  lint:

    env:
      TOXENV: lint

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v1
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools tox
    - name: Lint
      run: |
        python -m tox

  documents:

    env:
      TOXENV: documents

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v1
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip setuptools tox
    - name: Install Aspell
      run: |
        sudo apt-get install aspell aspell-en
    - name: Build documents
      run: |
        python -m tox
