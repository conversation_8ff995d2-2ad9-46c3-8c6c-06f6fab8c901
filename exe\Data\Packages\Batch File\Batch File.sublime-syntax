%YAML 1.2
---
# https://www.sublimetext.com/docs/syntax.html
# https://stackoverflow.com/questions/4094699/how-does-the-windows-command-interpreter-cmd-exe-parse-scripts/4095133#4095133
name: Batch File
scope: source.dosbatch
version: 2

file_extensions:
  - bat
  - cmd

hidden_file_extensions:
  - .bat
  - .cmd

first_line_match: |-
  (?xi:
    ^ [@\s]* (?:\:|rem) .*? -\*- .*? \b(?:bat|cmd)\b .*? -\*-  # editorconfig
  | ^ [@\s]* (?:if \s .*? [@(\s])? (?:echo \s+ (?:on|off|=)|(?:set|end)local) \b
  )

###############################################################################

variables:
  # A character that, when unquoted, separates words. A metacharacter is a
  # space, tab, newline, or one of the following characters:
  # ‘|’, ‘&’, ‘,’, ‘;’, ‘<’, or ‘>’.
  metachar: '[\s=,;{{redir_or_eoc_char}}]'

  # Keywords
  keyword_break: (?![^{{metachar}}(.:\\/])

  # Command names are non-posix identifiers
  cmd_begin: (?={{cmd_start}})
  cmd_break: (?!{{cmd_char}})
  cmd_start: '[^{{metachar}}()]'
  cmd_char: '[^{{metachar}}(]'

  # Command arguments are identifiers, which may start with interpolation.
  opt_punctuation: (?:--?|//?){{arg_begin}}
  arg_begin: (?={{arg_char}})
  arg_break: (?!{{arg_char}})
  arg_char: '[^{{metachar}}:]'

  # Command statement terminators and redirections
  redir_or_eoc: (?=\s*{{redir_or_eoc_char}})
  redir_or_eoc_char: '[<>{{eoc_char}}]'
  eoc: (?=\s*{{eoc_char}})
  eoc_char: '[\n|&]'

  label_comment: ':+[^{{label_start}}]'
  label_start: '[^{{metachar}}:+]'

  path_terminator_chars: '[\s,;"{{redir_or_eoc_char}}]'
  path_terminators: (?={{path_terminator_chars}})

  set_arithmetic_operators_unquoted: (?:\+|-|\*|/|%%|~)
  set_arithmetic_operators_quoted: (?:\||<<|>>|&|\^)
  set_quoted_end: \"(?![^"{{redir_or_eoc_char}}]*\")

  expansion_begin: |-
    (?x:
      %
      (?=
        # anything but no colon, percentage or command terminator char
        ( [^:%{{redir_or_eoc_char}}]
        # otherwise must not look like followed by a command
        | [{{redir_or_eoc_char}}](?!\s*\w+[\s{{redir_or_eoc_char}}])
        )+
        # don't check substrings and substitutions
        (:~?[^%]*)? %
      )
    )

  delayed_expansion_begin: |-
    (?x:
      !
      (?=
        # anything but no colon, exclamation mark or command terminator char
        ( [^:!{{redir_or_eoc_char}}]
        # otherwise must not look like followed by a command
        | [{{redir_or_eoc_char}}](?!\s*\w+[\s{{redir_or_eoc_char}}])
        )+
        # don't check substrings and substitutions
        (:~?[^!]*)? !
      )
    )

  parameter: (%)(?:(?:~([fdpnxsatz]|\$PATH:)*)?\d|\*)

  var_parameter: (%%)(?:~[fdpnxsatz]*)?{{var_char}}{{var_break}}
  var_name: '{{var_char}}+{{var_break}}'
  var_break: (?![^%{{metachar}}])
  var_char:  (?:{{var_literal}}|{{var_escape}})
  var_escape: \^[^=\s]
  var_literal: '[^=\s"^{{redir_or_eoc_char}}]'

  # An unquoted word, which may contain escapes or interpolation,
  # but no redirections. It is terminated by meta-chars or quoted words
  unquoted_word_begin: (?={{unquoted_word_char}})
  unquoted_word_break: (?!{{unquoted_word_char}})
  unquoted_word_char: '[^"{{metachar}}]'

  # Words are separated by meta-chars, only.
  # They may contain quoted regions, escaped chars or interpolations.
  word_break: (?!{{word_char}})
  word_char: '[^{{metachar}}]'

  builtin_commands: |-
    \b(?xi:
      assoc | break | cd | chdir | cls | color | copy | date | del | dir
    | dpath | erase | ftype | keys | md | mkdir | mklink | move | path
    | popd | prompt | pushd | ren | rename | rd | rmdir | set | shift | start
    | time | title | type | ver | verify | vol
    ){{keyword_break}}

  operator_comparison: (?xi:equ | neq | lss | leq | gtr | geq){{keyword_break}}

###############################################################################

contexts:
  main:
    - include: comments
    - include: statements

  statements:
    - include: blocks
    - include: labels
    - include: operators
    - include: redirections
    - include: control
    - include: commands

  commands:
    - include: cmd-echo
    - include: cmd-rem
    - include: cmd-set
    - include: cmd-mode
    - include: cmd-title
    - include: cmd-builtin
    - include: cmd-other

  control:
    - include: ctl-call
    - include: ctl-if
    - include: ctl-endlocal
    - include: ctl-exit
    - include: ctl-for
    - include: ctl-goto
    - include: ctl-pause
    - include: ctl-setlocal

###[ COMMENTS ]###############################################################

  comments:
    # https://ss64.com/nt/rem.html
    - match: '{{label_comment}}'
      scope: punctuation.definition.comment.dosbatch
      push: comment-body

  comment-body:
    - meta_scope: comment.line.colon.dosbatch
    - include: line-continuations
    - match: $\n?
      pop: 1

  ignored-tail-inner:
    - match: \s*(.*)$
      captures:
        1: comment.line.ignored.dosbatch
      pop: 1

  ignored-tail-outer:
    - include: redirections
    - include: eoc-pop
    - match: (?=\S)
      set: ignored-tail-body

  ignored-tail-body:
    - meta_scope: comment.line.ignored.dosbatch
    - include: redirection-interpolations
    - include: eoc-pop
    - match: '{{unquoted_word_begin}}'
      push: unquoted-word-end

###[ LABELS ]#################################################################

  maybe-label:
    - match: \:(?={{label_start}})
      scope: entity.name.label.dosbatch punctuation.definition.label.dosbatch
      set: label-name
    - include: else-pop

  labels:
    - match: ^\s*(:)(?={{label_start}})
      captures:
        1: entity.name.label.dosbatch punctuation.definition.label.dosbatch
      push: label-name

  label-name:
    - meta_content_scope: entity.name.label.dosbatch
    - match: '{{word_break}}'
      set: ignored-tail-outer
    - include: escaped-characters

###[ BLOCKS ]#################################################################

  embedded:
    - match: \'
      scope: meta.embedded.dosbatch punctuation.section.embedded.begin.dosbatch
      embed: commands
      embed_scope: meta.embedded.dosbatch source.dosbatch.embedded
      escape: \'(?=\s*['{{redir_or_eoc_char}}])
      escape_captures:
        0: meta.embedded.dosbatch punctuation.section.embedded.end.dosbatch
    - match: \`
      scope: meta.embedded.dosbatch punctuation.section.embedded.begin.dosbatch
      embed: commands
      embed_scope: meta.embedded.dosbatch source.dosbatch.embedded
      escape: \`(?=\s*[`{{redir_or_eoc_char}}])
      escape_captures:
        0: meta.embedded.dosbatch punctuation.section.embedded.end.dosbatch

  statement:
    - match: \(
      scope: punctuation.section.block.begin.dosbatch
      set: Packages/Batch File/Batch File (Compound).sublime-syntax#block-common
    - include: eoc-pop
    - include: statements

  blocks:
    - match: \(
      scope: punctuation.section.block.begin.dosbatch
      push: Packages/Batch File/Batch File (Compound).sublime-syntax#block-common
    - match: \)
      scope: invalid.illegal.stray.dosbatch

  block-common:
    - meta_scope: meta.block.dosbatch
    - match: \)
      scope: punctuation.section.block.end.dosbatch
      pop: 1
    - include: main

  groups:
    - match: \(
      scope: punctuation.section.group.begin.dosbatch
      push: Packages/Batch File/Batch File (Compound).sublime-syntax#group-common

  group-common:
    - meta_scope: meta.group.dosbatch
    - match: \)
      scope: punctuation.section.group.end.dosbatch
      pop: 1
    - include: groups
    - include: separator-semicolon
    - include: separator-comma
    - include: redirections
    - include: cmd-args-values

###[ CONTROL CONDITIONAL ]####################################################

  ctl-if:
    # https://ss64.com/nt/if.html
    - match: (?i:if){{keyword_break}}
      scope: keyword.control.conditional.if.dosbatch
      push:
        - ctl-if-meta
        - ctl-if-condition
    - match: (?i:else){{keyword_break}}
      scope: invalid.illegal.stray.dosbatch

  ctl-if-meta:
    - meta_include_prototype: false
    - meta_scope: meta.statement.conditional.dosbatch
    - include: immediately-pop

  ctl-if-condition:
    - include: eoc-pop
    - include: cmd-arg-help
    - match: (?i:(/)i){{arg_break}}
      scope: variable.parameter.option.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
      set:
        - ctl-else
        - statement
        - maybe-operand
        - expect-operator-comparison
        - maybe-operand
        - maybe-operator-not
    - match: (?=\S)
      set:
        - ctl-else
        - statement
        - ctl-if-checks
        - maybe-operator-not

  ctl-if-checks:
    - include: eoc-pop
    - match: (?i:errorlevel|cmdextversion){{keyword_break}}
      scope: variable.language.dosbatch
      set:
        - maybe-operand
        - maybe-operator-equal
    - match: (?i:exist){{keyword_break}}
      scope: support.function.builtin.dosbatch
      set: path-pattern
    - match: (?i:defined){{keyword_break}}
      scope: support.function.builtin.dosbatch
      set: maybe-variable
    - match: (?=\S)
      set:
        - maybe-operand
        - expect-operator-comparison
        - maybe-operand

  maybe-operand:
    - include: unquoted-eol-pop
    - match: (?=\S)
      set: operand-body

  operand-body:
    - include: groups
    - include: cmd-args-values
    - include: immediately-pop

  maybe-variable:
    - match: '{{var_name}}'
      scope: variable.other.readwrite.dosbatch
      pop: 1
    - include: else-pop

  expect-operator-comparison:
    - match: '{{operator_comparison}}|=='
      scope: keyword.operator.comparison.dosbatch
      pop: 1
    - include: illegal-token-pop

  maybe-operator-equal:
    - match: ==
      scope: keyword.operator.comparison.dosbatch
      pop: 1
    - match: '{{operator_comparison}}'
      scope: invalid.illegal.unexpected.dosbatch
      pop: 1
    - include: else-pop

  maybe-operator-not:
    - match: (?i:not){{keyword_break}}
      scope: keyword.operator.logical.dosbatch
      pop: 1
    - include: else-pop

  ctl-else:
    - match: (?i:else){{keyword_break}}
      scope: keyword.control.conditional.else.dosbatch
      set: statement
    - include: expect-eoc

###[ CONTROL FOR ]############################################################

  ctl-for:
    # https://ss64.com/nt/for.html
    - match: (?i:for){{keyword_break}}
      scope: keyword.control.loop.for.dosbatch
      push:
        - ctl-for-meta
        - statement
        - ctl-for-do
        - ctl-for-set
        - ctl-for-in
        - ctl-for-parameter
        - ctl-for-switches

  ctl-for-meta:
    - meta_include_prototype: false
    - meta_scope: meta.statement.loop.for.dosbatch
    - include: immediately-pop

  ctl-for-switches:
    - include: cmd-arg-help
    # https://ss64.com/nt/for_d.html
    - match: (?i:(/)d){{arg_break}}
      scope: variable.parameter.option.dir.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
    # https://ss64.com/nt/for_l.html
    - match: (?i:(/)l){{arg_break}}
      scope: variable.parameter.option.range.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
    # https://ss64.com/nt/for_f.html
    - match: (?i:(/)f){{arg_break}}
      scope: variable.parameter.option.files.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
      push: ctl-for-maybe-f-args
    # https://ss64.com/nt/for_r.html
    - match: (?i:(/)r){{arg_break}}
      scope: variable.parameter.option.recursive.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
      push: ctl-for-maybe-r-args
    - match: /{{arg_char}}+
      scope: invalid.illegal.parameter.dosbatch
      pop: 1
    - include: else-pop

  ctl-for-maybe-r-args:
    - match: (?={{var_parameter}})
      pop: 1
    - include: cmd-arg-help
    - include: path-pattern

  ctl-for-maybe-f-args:
    - match: \"
      scope: punctuation.definition.string.begin.dosbatch
      set: ctl-for-f-args
    - include: else-pop

  ctl-for-f-args:
    - meta_include_prototype: false
    - meta_scope: meta.string.dosbatch string.quoted.double.dosbatch
    - include: string-common
    - match: =
      scope: keyword.operator.assignment.dosbatch
    - match: (?:delims|eol|skip|tokens){{arg_break}}
      scope: constant.language.dosbatch
    - match: usebackq{{arg_break}}
      scope: constant.language.dosbatch

  ctl-for-parameter:
    - match: (?=(?i:in|do){{keyword_break}}|\()
      pop: 1
    - match: '{{var_parameter}}'
      scope: variable.other.readwrite.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
      pop: 1
    - include: unquoted-eol-pop
    - match: \S
      scope: invalid.illegal.variable.dosbatch

  ctl-for-in:
    - match: (?i:in){{keyword_break}}
      scope: keyword.operator.logical.dosbatch
      pop: 1
    - include: else-pop

  ctl-for-set:
    - match: \(
      scope: punctuation.section.set.begin.dosbatch
      set: Packages/Batch File/Batch File (Compound).sublime-syntax#ctl-for-set-body
    - include: else-pop

  ctl-for-set-body:
    - meta_scope: meta.set.dosbatch
    - match: \)
      scope: punctuation.section.set.end.dosbatch
      pop: 1
    - include: invalid-operators
    - include: separator-comma
    - include: embedded
    - include: cmd-args-values

  ctl-for-do:
    - match: (?i:do){{keyword_break}}
      scope: keyword.control.loop.do.dosbatch
      pop: 1
    - include: else-pop

###[ CONTROL CALL ]###########################################################

  ctl-call:
    # https://ss64.com/nt/call.html
    - match: (?i:call){{keyword_break}}
      scope:
        meta.function-call.dosbatch
        keyword.control.flow.call.dosbatch
      push: ctl-call-identifier

  ctl-call-identifier:
    - meta_content_scope: meta.function-call.dosbatch
    - match: ':'
      scope: punctuation.definition.variable.dosbatch
      set: ctl-call-label
    - include: cmd-arg-help
    - include: else-pop

  ctl-call-label:
    - meta_scope: meta.function-call.identifier.dosbatch variable.label.dosbatch
    - match: '{{word_break}}'
      set:
        - cmd-args-meta
        - cmd-args-body
    - include: line-continuations
    - include: escaped-characters
    - include: variables

###[ CONTROL EXIT ]###########################################################

  ctl-exit:
    # https://ss64.com/nt/exit.html
    - match: (?i:exit){{keyword_break}}
      scope: keyword.control.flow.exit.dosbatch
      push:
        - ctl-exit-meta
        - ctl-exit-args

  ctl-exit-meta:
    - meta_include_prototype: false
    - meta_scope: meta.command.exit.dosbatch
    - include: immediately-pop

  ctl-exit-args:
    - match: (?i:(/)b){{arg_break}}
      scope: variable.parameter.option.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
    - include: cmd-arg-help
    - include: numbers
    - include: variables
    - include: expect-eoc

###[ CONTROL GOTO ]###########################################################

  ctl-goto:
    # https://ss64.com/nt/goto.html
    - match: (?i:goto){{keyword_break}}
      scope: keyword.control.flow.goto.dosbatch
      push:
        - ctl-goto-meta
        - ctl-goto-args

  ctl-goto-meta:
    - meta_include_prototype: false
    - meta_scope: meta.command.goto.dosbatch
    - include: immediately-pop

  ctl-goto-args:
    - include: redirections
    - include: cmd-arg-help
    - include: eoc-pop
    - match: (:)(?i:(eof)){{word_break}}
      scope: variable.label.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
        2: keyword.control.flow.return.dosbatch
      pop: 1
    - match: (:)?(?={{label_start}})
      scope: punctuation.definition.label.dosbatch
      set: ctl-goto-label

  ctl-goto-label:
    - meta_scope: variable.label.dosbatch
    - include: line-continuations
    - include: label-name
    - include: variables

###[ CONTROL PAUSE ]##########################################################

  ctl-pause:
    # https://ss64.com/nt/pause.html
    - match: (?i:pause){{keyword_break}}
      scope: keyword.control.flow.pause.dosbatch
      push:
        - ctl-pause-meta
        - ctl-pause-args

  ctl-pause-meta:
    - meta_include_prototype: false
    - meta_scope: meta.command.pause.dosbatch
    - include: immediately-pop

  ctl-pause-args:
    - include: redirections
    - include: cmd-arg-help
    - include: expect-eoc

###[ CONTEXT SETLOCAL ]#######################################################

  ctl-setlocal:
    # https://ss64.com/nt/setlocal.html
    - match: (?i:setlocal){{keyword_break}}
      scope: keyword.context.setlocal.dosbatch
      push:
        - ctl-setlocal-meta
        - ctl-setlocal-args

  ctl-setlocal-meta:
    - meta_include_prototype: false
    - meta_scope: meta.command.setlocal.dosbatch
    - include: immediately-pop

  ctl-setlocal-args:
    - match: |-
        \b(?xi:
          EnableDelayedExpansion | DisableDelayedExpansion |
          EnableExtensions | DisableExtensions
        ){{arg_break}}
      scope: constant.language.dosbatch
    - include: redirections
    - include: cmd-arg-help
    - include: expect-eoc

###[ CONTEXT ENDLOCAL ]#######################################################

  ctl-endlocal:
    # https://ss64.com/nt/endlocal.html
    - match: (?i:endlocal){{keyword_break}}
      scope: keyword.context.endlocal.dosbatch
      push:
        - ctl-endlocal-meta
        - ctl-endlocal-args

  ctl-endlocal-meta:
    - meta_include_prototype: false
    - meta_scope: meta.command.endlocal.dosbatch
    - include: immediately-pop

  ctl-endlocal-args:
    - include: redirections
    - include: cmd-arg-help
    - include: expect-eoc

###[ COMMANDS ]###############################################################

  cmd-builtin:
    - match: '{{builtin_commands}}'
      scope:
        meta.function-call.identifier.dosbatch
        support.function.builtin.dosbatch
      push:
        - cmd-args-meta
        - cmd-args-body

  cmd-other:
    - match: '{{cmd_begin}}'
      push:
        - cmd-other-identifier
        - path-pattern-relative

  cmd-other-identifier:
    - meta_scope:
        meta.function-call.identifier.dosbatch
        variable.function.dosbatch
    - match: '{{cmd_break}}'
      set:
        - cmd-args-meta
        - cmd-args-body
    - match: \"
      scope: punctuation.definition.string.begin.dosbatch
      push:
        - cmd-other-identifier-quoted-common
        - path-pattern-relative
    - include: path-pattern-unquoted-content
    - include: line-continuations

  cmd-other-identifier-quoted-common:
    - clear_scopes: 1
    - meta_include_prototype: false
    - meta_scope: meta.string.dosbatch variable.function.dosbatch
    - include: path-pattern-quoted-body

  cmd-args-meta:
    - meta_include_prototype: false
    - meta_content_scope: meta.function-call.arguments.dosbatch
    - include: immediately-pop

  cmd-args-body:
    - include: redirections
    - include: eoc-pop
    - include: cmd-arg-help
    - include: cmd-args-options
    - include: cmd-args-values

  cmd-arg-help:
    - match: (/)\?{{arg_break}}
      scope: variable.parameter.option.help.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
      set: ignored-tail-outer

  cmd-args-options:
    # The shell basically just passes words to a called command.
    # The command is responsible to parse and interpret each word.
    #
    # A word may be
    # - an option `/b` or `-b`
    # - a value `foo`
    # - a key-value pair `/key:value` or `--key=value`
    #
    # Interpretation includes whether quotation marks
    # are treated literal or whether they are removed.
    #
    # This context applies various heuristics to try to highlight
    # different kinds of command line arguments and their values.
    - match: '{{opt_punctuation}}'
      scope:
        meta.parameter.option.dosbatch
        variable.parameter.option.dosbatch
        punctuation.definition.variable.dosbatch
      push: cmd-args-option-identifier

  cmd-args-option-identifier:
    - meta_content_scope:
        meta.parameter.option.dosbatch
        variable.parameter.option.dosbatch
    - match: '[=:]'
      scope:
        meta.parameter.dosbatch
        keyword.operator.assignment.dosbatch
      set: cmd-args-option-value
    - include: quoted-words
    - include: unquoted-escapes-and-interpolations
    - include: unquoted-word-end

  cmd-args-option-value:
    - meta_content_scope: meta.parameter.value.dosbatch
    - include: cmd-args-values
    - include: immediately-pop

  cmd-args-values:
    - include: constants
    - include: numbers
    - match: '{{arg_begin}}'
      branch_point: cmd-args-values
      branch:
        - cmd-args-value-chars
        - path-pattern

  cmd-args-value-chars:
    - meta_scope: meta.string.dosbatch string.unquoted.dosbatch
    - match: (?=(\.\.?|\:)[\\%{{metachar}}]|[?*]|\\(?!"))
      fail: cmd-args-values
    - include: quoted-words
    - include: unquoted-escapes-and-interpolations
    - include: unquoted-word-end

  cmd-arg-illegal-options:
    - match: /{{arg_char}}+
      scope: invalid.illegal.parameter.dosbatch

###[ COMMAND ECHO ]###########################################################

  cmd-echo:
    # https://ss64.com/nt/echo.html
    - match: (?i:echo){{keyword_break}}
      scope:
        meta.command.echo.dosbatch
        meta.function-call.identifier.dosbatch
        support.function.builtin.dosbatch
      push: cmd-echo-args

  cmd-echo-args:
    - meta_content_scope: meta.command.echo.dosbatch meta.function-call.arguments.dosbatch
    - include: eoc-pop
    - match: \s*((/)\?){{arg_break}}
      captures:
        1: variable.parameter.option.help.dosbatch
        2: punctuation.definition.variable.dosbatch
      push: ignored-tail-outer
    - match: \s*((?i:on|off)){{redir_or_eoc}}
      captures:
        1: constant.language.dosbatch
      push: ignored-tail-outer
    - match: '[.:]'
      scope: punctuation.separator.arguments.dosbatch
      set: cmd-echo-output
    - match: \s+(?!\^\n)
      set: cmd-echo-output

  cmd-echo-output:
    - meta_scope: meta.command.echo.dosbatch meta.function-call.arguments.dosbatch
    - meta_content_scope: meta.string.dosbatch string.unquoted.dosbatch
    - include: quoted-words
    - include: redirection-interpolations
    - include: unquoted-words
    - include: eoc-pop

###[ COMMAND MODE ]###########################################################

  cmd-mode:
    - match: (?i:mode){{keyword_break}}
      scope:
        meta.function-call.identifier.dosbatch
        support.function.external.dosbatch
      push:
        - cmd-mode-meta
        - cmd-mode-args

  cmd-mode-meta:
    - meta_include_prototype: false
    - meta_scope: meta.command.mode.dosbatch
    - meta_content_scope: meta.function-call.arguments.dosbatch
    - include: immediately-pop

  cmd-mode-args:
    - include: redirections
    - include: eoc-pop
    - match: (?i:COM\d+|CON|LPT\d+):?{{arg_break}}
      scope: variable.language.device.dosbatch
    # known option values
    - match: (?i:on|off|hs|tg){{arg_break}}
      scope: constant.language.dosbatch
    # COM/LPT options
    - match: (?i:BAUD|PARITY|DATA|STOP|to|xon|odsr|octs|dtr|rts|idsr){{arg_break}}
      scope: variable.parameter.option.dosbatch
    # CON options
    - match: (?i:CP|SELECT|COLS|LINES|RATE|DELAY){{arg_break}}
      scope: variable.parameter.option.dosbatch
    - match: =
      scope: keyword.operator.assignment.dosbatch
    - include: cmd-arg-help
    - include: cmd-args-options
    - include: cmd-args-values

###[ COMMAND REM ]############################################################

  cmd-rem:
    # https://ss64.com/nt/rem.html
    - match: (?i:rem){{keyword_break}}
      scope: keyword.declaration.rem.dosbatch
      push:
        - cmd-rem-meta
        - cmd-rem-body

  cmd-rem-meta:
    - meta_include_prototype: false
    - meta_scope: meta.command.rem.dosbatch
    - include: immediately-pop

  cmd-rem-body:
    - meta_include_prototype: false
    - include: cmd-arg-help
    - include: unquoted-eol-pop
    - match: (?=\S)
      set:
        - cmd-rem-comment-body
        - cmd-rem-comment-begin

  cmd-rem-comment-begin:
    # In REM comments only the first continuation directly following
    # the first token is taken into account.
    - meta_include_prototype: false
    - match: (\^)\n
      captures:
        1: punctuation.separator.continuation.line.dosbatch
      set: line-continuation-body
    - match: (?={{metachar}})
      pop: 1

  cmd-rem-comment-body:
    - meta_include_prototype: false
    # meta_content_scope is used since rem should not be
    # highlighted as a comment, but a command
    - meta_content_scope: comment.line.rem.dosbatch
    - match: $\n?
      scope: comment.line.rem.dosbatch
      pop: 1

###[ COMMAND SET ]############################################################

  cmd-set:
    # https://ss64.com/nt/set.html
    - match: (?i:set){{keyword_break}}
      scope: support.function.builtin.dosbatch
      push:
        - cmd-set-meta
        - cmd-set-type

  cmd-set-meta:
    - meta_include_prototype: false
    - meta_scope: meta.command.set.dosbatch
    - include: immediately-pop

  cmd-set-type:
    - include: redirections
    - include: eoc-pop
    - include: cmd-set-arithmetic
    - include: cmd-set-prompt
    - include: cmd-arg-help
    - include: cmd-arg-illegal-options
    - include: cmd-set-quoted
    - include: cmd-set-unquoted

  cmd-set-variable-inner-common:
    - include: cmd-set-variable-common
    - include: quoted-eol-pop

  cmd-set-variable-outer-common:
    - include: cmd-set-variable-common
    - include: unquoted-var-pop

  cmd-set-variable-common:
    - match: \^=
      scope: invalid.illegal.parameter.dosbatch
    - match: \^\S

  cmd-set-value-inner-common:
    - include: quoted-escapes-and-interpolations
    - include: quoted-eol-pop

  cmd-set-value-outer-common:
    - include: redirection-interpolations
    - include: unquoted-words
    - include: eoc-pop

###[ COMMAND SET "VARIABLE=VALUE" ]############################################

  cmd-set-quoted:
    # Quoted assignments require some magic to mimic correct interpreter
    # behaviour, with regards to line continuation and quotation marks.
    #
    # Even though quotation marks (except the very last one) are not removed
    # from an expression, they still effect whether line continuation or
    # expression terminators are effective or not.
    #
    # A line continuation operator following an even occurrence of a quote
    # causes the following line to be concatenated.
    #
    # The very last quotation mark in front of an effective expression terminator
    # finalizes the value. All content behind is ignored up to the
    # expression terminator.
    #
    # An expression terminator is the end of a line, a logical or redirection
    # operator. The latter two are effective only when not quoted.
    #
    # The contexts of this section try their best to implement the described
    # behavior. The main reason for all that effort is to provide some
    # assistance to avoid otherwise hard to debug interpreter issues.
    #
    # SET "variable"=
    # SET "variable="
    # SET "variable=string"
    # SET "variable=string with literal "quotes" !"
    - match: \"
      scope: meta.string.dosbatch punctuation.definition.string.begin.dosbatch
      set: cmd-set-quoted-variable-inner

  cmd-set-quoted-variable-inner:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch variable.other.readwrite.dosbatch
    - match: =
      scope: meta.string.dosbatch keyword.operator.assignment.dosbatch
      set: cmd-set-quoted-value-inner
    - match: \"
      scope: meta.string.dosbatch variable.other.readwrite.dosbatch
      set: cmd-set-quoted-variable-outer
    - include: cmd-set-variable-inner-common

  cmd-set-quoted-variable-outer:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch variable.other.readwrite.dosbatch
    - match: =
      scope: meta.string.dosbatch keyword.operator.assignment.dosbatch
      set: cmd-set-quoted-value-outer
    - match: \"
      scope: meta.string.dosbatch variable.other.readwrite.dosbatch
      set: cmd-set-quoted-variable-inner
    - include: cmd-set-variable-outer-common

  cmd-set-quoted-value-inner:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.unquoted.dosbatch
    # match the very last quotation mark
    - match: (?={{set_quoted_end}})
      branch_point: cmd-set-quoted-value-inner
      branch:
        - cmd-set-quoted-value-inner-continue
        - cmd-set-quoted-value-inner-end
    - match: \"
      scope: meta.string.dosbatch string.unquoted.dosbatch
      set: cmd-set-quoted-value-outer
    - include: cmd-set-value-inner-common

  cmd-set-quoted-value-inner-continue:
    - meta_include_prototype: false
    - match: \"
      set: cmd-set-quoted-value-inner-nextline

  cmd-set-quoted-value-inner-nextline:
    - meta_include_prototype: false
    - match: '{{set_quoted_end}}'
      pop: 1
    - match: '{{eoc}}'
      fail: cmd-set-quoted-value-inner
    - include: quoted-escapes-and-interpolations
    - include: line-continuations

  cmd-set-quoted-value-inner-end:
    - meta_include_prototype: false
    - clear_scopes: 1
    - match: \"
      scope: punctuation.definition.string.end.dosbatch
      pop: 2
      set: ignored-tail-outer

  cmd-set-quoted-value-outer:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.unquoted.dosbatch
    # match the very last quotation mark of the current line
    - match: \"(?=[^"]*$)
      scope: meta.string.dosbatch punctuation.definition.string.end.dosbatch
      pop: 1
      set: ignored-tail-inner
    - match: \"
      scope: meta.string.dosbatch string.unquoted.dosbatch
      set: cmd-set-quoted-value-inner
    - include: cmd-set-value-outer-common

###[ COMMAND SET VARIABLE=VALUE ]##############################################

  cmd-set-unquoted:
    # SET variable
    # SET variable=string
    # SET variable="string"
    - match: (?=\S)
      set: cmd-set-unquoted-variable-outer

  cmd-set-unquoted-variable-inner:
    - meta_include_prototype: false
    - meta_content_scope: variable.other.readwrite.dosbatch
    - match: =
      scope: keyword.operator.assignment.dosbatch
      set: cmd-set-unquoted-value-inner
    - match: \"
      scope: variable.other.readwrite.dosbatch
      set: cmd-set-unquoted-variable-outer
    - include: cmd-set-variable-inner-common

  cmd-set-unquoted-variable-outer:
    - meta_include_prototype: false
    - meta_content_scope: variable.other.readwrite.dosbatch
    - match: =
      scope: keyword.operator.assignment.dosbatch
      set: cmd-set-unquoted-value-outer
    - match: \"
      scope: variable.other.readwrite.dosbatch
      set: cmd-set-unquoted-variable-inner
    - include: cmd-set-variable-outer-common

  cmd-set-unquoted-value-inner:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.unquoted.dosbatch
    - match: \"
      scope: meta.string.dosbatch string.unquoted.dosbatch
      set: cmd-set-unquoted-value-outer
    - include: cmd-set-value-inner-common

  cmd-set-unquoted-value-outer:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.unquoted.dosbatch
    - match: \"
      scope: meta.string.dosbatch string.unquoted.dosbatch
      set: cmd-set-unquoted-value-inner
    - include: cmd-set-value-outer-common

###[ COMMAND SET /A ]#########################################################

  cmd-set-arithmetic:
    # Arithmetic expressions are a bit simpler as all quotation marks
    # 1. have the same meaning
    # 2. are removed from output
    # 3. split an expression into sections which support
    #    a) line continuation `^`
    #    b) command terminations via `&` or `|`
    #    and those which don't.
    #
    # The challange here is to correctly switch contexts to maintain both
    # "quoted" and "grouped" states at the same time.
    #
    # Variables may not contain whitespace nor any kind of operator char.
    #
    # SET /A "variable"=expression
    # SET /A "variable=expression"
    # SET /A variable=expression
    - match: (?i:(/)a){{arg_break}}
      scope: variable.parameter.option.expression.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
      set: cmd-set-arithmetic-start

  cmd-set-arithmetic-meta:
    - meta_include_prototype: false
    - meta_scope: meta.expression.dosbatch
    - include: immediately-pop

  cmd-set-arithmetic-start:
    - include: cmd-arg-help
    - match: \"
      scope: meta.string.dosbatch punctuation.definition.string.begin.dosbatch
      set:
        - cmd-set-arithmetic-meta
        - cmd-set-arithmetic-quoted
    - match: (?=\S)
      set:
        - cmd-set-arithmetic-meta
        - cmd-set-arithmetic-unquoted

  cmd-set-arithmetic-quoted:
    - meta_content_scope: meta.string.dosbatch
    - match: \"
      scope: meta.string.dosbatch punctuation.definition.string.end.dosbatch
      set: cmd-set-arithmetic-unquoted
    - match: \(
      scope: meta.group.dosbatch punctuation.section.group.begin.dosbatch
      set: cmd-set-arithmetic-quoted-first-group
    - include: cmd-set-arithmetic-quoted-common
    - include: quoted-eol-pop

  cmd-set-arithmetic-quoted-first-group:
    - meta_scope: meta.string.dosbatch
    - meta_content_scope: meta.group.dosbatch
    - match: \"
      scope: meta.group.dosbatch punctuation.definition.string.end.dosbatch
      set: cmd-set-arithmetic-unquoted-group
    - match: \(
      scope: meta.group.dosbatch punctuation.section.group.begin.dosbatch
      push: cmd-set-arithmetic-quoted-group
    - match: \)
      scope: meta.group.dosbatch punctuation.section.group.end.dosbatch
      set: cmd-set-arithmetic-quoted
    - include: cmd-set-arithmetic-quoted-common
    - include: quoted-eol-pop

  cmd-set-arithmetic-quoted-group:
    - meta_content_scope: meta.group.dosbatch
    - match: \"
      scope: meta.group.dosbatch punctuation.definition.string.end.dosbatch
      set: cmd-set-arithmetic-unquoted-group
    - match: \(
      scope: meta.group.dosbatch punctuation.section.group.begin.dosbatch
      push: cmd-set-arithmetic-quoted-group
    - match: \)
      scope: meta.group.dosbatch punctuation.section.group.end.dosbatch
      pop: 1
    - include: cmd-set-arithmetic-quoted-common
    - include: quoted-eol-pop

  cmd-set-arithmetic-quoted-common:
    - match: '{{set_arithmetic_operators_quoted}}='
      scope: keyword.operator.assignment.augmented.dosbatch
    - match: '{{set_arithmetic_operators_quoted}}'
      scope: keyword.operator.arithmetic.dosbatch
    - include: cmd-set-arithmetic-unquoted-common

  cmd-set-arithmetic-unquoted:
    - match: \"
      scope: punctuation.definition.string.begin.dosbatch
      push: cmd-set-arithmetic-quoted
    - match: \(
      scope: meta.group.dosbatch punctuation.section.group.begin.dosbatch
      push: cmd-set-arithmetic-unquoted-group
    - include: cmd-set-arithmetic-unquoted-common
    - include: eoc-pop

  cmd-set-arithmetic-unquoted-group:
    - meta_content_scope: meta.group.dosbatch
    # Note: must be included before \) pattern to support compound termination
    - include: eoc-pop
    - match: \"
      scope: meta.group.dosbatch punctuation.definition.string.begin.dosbatch
      set: cmd-set-arithmetic-quoted-first-group
    - match: \(
      scope: meta.group.dosbatch punctuation.section.group.begin.dosbatch
      push: cmd-set-arithmetic-unquoted-group
    - match: \)
      scope: meta.group.dosbatch punctuation.section.group.end.dosbatch
      pop: 1
    - include: cmd-set-arithmetic-unquoted-common

  cmd-set-arithmetic-unquoted-common:
    - match: '{{set_arithmetic_operators_unquoted}}='
      scope: keyword.operator.assignment.augmented.dosbatch
    - match: (\^)(\^=)
      captures:
        1: constant.character.escape.dosbatch
        2: keyword.operator.assignment.augmented.dosbatch
    - match: '{{set_arithmetic_operators_unquoted}}'
      scope: keyword.operator.arithmetic.dosbatch
    - match: (\^)(\^)
      captures:
        1: constant.character.escape.dosbatch
        2: keyword.operator.arithmetic.dosbatch
    - match: =
      scope: keyword.operator.assignment.dosbatch
    - match: '!'
      scope: keyword.operator.logical.dosbatch
    - include: numbers
    - include: separator-comma
    - include: escaped-characters
    - include: variables
    - match: '[^-+*/%~=()"''^{{metachar}}]+'
      scope: variable.other.readwrite.dosbatch

###[ COMMAND SET /P ]#########################################################

  cmd-set-prompt:
    - match: (?i:(/)p){{arg_break}}
      scope: variable.parameter.option.prompt.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
      set: cmd-set-prompt-start

  cmd-set-prompt-meta:
    - meta_include_prototype: false
    - meta_scope: meta.prompt.dosbatch
    - include: immediately-pop

  cmd-set-prompt-start:
    - include: cmd-arg-help
    - include: cmd-set-quoted-prompt
    - include: cmd-set-unquoted-prompt

###[ COMMAND SET /P "VARIABLE=PROMPT" ]#######################################

  cmd-set-quoted-prompt:
    # Quoted prompts are the most complicated part as they behave like static
    # assignments and additionally distinguish between quoted and unquoted
    # values. The first quotation mark after an assignment operator has special
    # meaning and is removed from output.
    #
    # Context switching works very much like the one with static assignments,
    # but with additional states caused by proper handling "quoted values".
    #
    # SET /P "variable"=
    # SET /P "variable="
    # SET /P "variable=unquoted prompt"
    # SET /P "variable=unquoted prompt with literal "quotes" !"
    # SET /P "variable="quoted prompt with literal "quotes"ignored
    - match: \"
      scope: punctuation.definition.prompt.begin.dosbatch
      set:
        - cmd-set-prompt-meta
        - cmd-set-quoted-prompt-variable-inner

  cmd-set-quoted-prompt-variable-inner:
    - meta_include_prototype: false
    - meta_content_scope: variable.other.readwrite.dosbatch
    - match: =
      scope: keyword.operator.assignment.dosbatch
      set: cmd-set-quoted-prompt-value-inner-start
    - match: \"
      scope: variable.other.readwrite.dosbatch
      set: cmd-set-quoted-prompt-variable-outer
    - include: cmd-set-variable-inner-common

  cmd-set-quoted-prompt-variable-outer:
    - meta_include_prototype: false
    - meta_content_scope: variable.other.readwrite.dosbatch
    - match: =
      scope: keyword.operator.assignment.dosbatch
      set: cmd-set-quoted-prompt-value-outer-start
    - match: \"
      scope: variable.other.readwrite.dosbatch
      set: cmd-set-quoted-prompt-variable-inner
    - include: cmd-set-variable-outer-common


  cmd-set-quoted-prompt-value-inner-start:
    - include: quoted-eol-pop
    - match: '{{set_quoted_end}}'
      scope: punctuation.definition.prompt.end.dosbatch
      pop: 1
      set: ignored-tail-outer
    - match: \"
      scope:
        meta.string.dosbatch string.quoted.double.dosbatch
        punctuation.definition.string.begin.dosbatch
      set: cmd-set-quoted-prompt-quoted-value-outer
    - match: (?=\S)
      set: cmd-set-quoted-prompt-unquoted-value-inner

  cmd-set-quoted-prompt-value-outer-start:
    - include: unquoted-eol-pop
    - match: '{{set_quoted_end}}'
      scope: punctuation.definition.prompt.end.dosbatch
      pop: 1
      set: ignored-tail-inner
    - match: \"
      scope:
        meta.string.dosbatch string.quoted.double.dosbatch
        punctuation.definition.string.begin.dosbatch
      set: cmd-set-quoted-prompt-quoted-value-inner
    - match: (?=\S)
      set: cmd-set-quoted-prompt-unquoted-value-outer


  cmd-set-quoted-prompt-quoted-value-inner:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.quoted.double.dosbatch
    # match the very last quotation mark
    - match: (?={{set_quoted_end}})
      branch_point: cmd-set-quoted-prompt-quoted-value-inner
      branch:
        - cmd-set-quoted-prompt-quoted-value-inner-continue
        - cmd-set-quoted-prompt-quoted-value-inner-end
    - match: \"
      scope: meta.string.dosbatch string.quoted.double.dosbatch
      set: cmd-set-quoted-prompt-quoted-value-outer
    - include: cmd-set-value-inner-common

  cmd-set-quoted-prompt-quoted-value-inner-continue:
    - meta_include_prototype: false
    - match: \"
      set: cmd-set-quoted-prompt-quoted-value-inner-nextline

  cmd-set-quoted-prompt-quoted-value-inner-nextline:
    - meta_include_prototype: false
    - match: '{{set_quoted_end}}'
      pop: 1
    - match: '{{eoc}}'
      fail: cmd-set-quoted-prompt-quoted-value-inner
    - include: quoted-escapes-and-interpolations
    - include: line-continuations

  cmd-set-quoted-prompt-quoted-value-inner-end:
    - meta_include_prototype: false
    - match: \"
      scope:
        punctuation.definition.prompt.end.dosbatch
        punctuation.definition.string.end.dosbatch
      pop: 2
      set: ignored-tail-outer

  cmd-set-quoted-prompt-quoted-value-outer:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.quoted.double.dosbatch
    - match: \"
      scope:
        meta.string.dosbatch string.quoted.double.dosbatch
        punctuation.definition.prompt.end.dosbatch
        punctuation.definition.string.end.dosbatch
      pop: 1
      set: ignored-tail-inner
    - match: \"
      scope: meta.string.dosbatch string.quoted.double.dosbatch
      set: cmd-set-quoted-prompt-quoted-value-inner
    - include: cmd-set-value-outer-common


  cmd-set-quoted-prompt-unquoted-value-inner:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.unquoted.dosbatch
    # match the very last quotation mark
    - match: (?={{set_quoted_end}})
      branch_point: cmd-set-quoted-prompt-unquoted-value-inner
      branch:
        - cmd-set-quoted-prompt-unquoted-value-inner-continue
        - cmd-set-quoted-prompt-unquoted-value-inner-end
    - match: \"
      scope: meta.string.dosbatch string.unquoted.dosbatch
      set: cmd-set-quoted-prompt-unquoted-value-outer
    - include: cmd-set-value-inner-common

  cmd-set-quoted-prompt-unquoted-value-inner-continue:
    - meta_include_prototype: false
    - match: \"
      set: cmd-set-quoted-prompt-unquoted-value-inner-nextline

  cmd-set-quoted-prompt-unquoted-value-inner-nextline:
    - meta_include_prototype: false
    - match: '{{set_quoted_end}}'
      pop: 1
    - match: '{{eoc}}'
      fail: cmd-set-quoted-prompt-unquoted-value-inner
    - include: quoted-escapes-and-interpolations
    - include: line-continuations

  cmd-set-quoted-prompt-unquoted-value-inner-end:
    - clear_scopes: 2
    - meta_include_prototype: false
    - match: \"
      scope: punctuation.definition.prompt.end.dosbatch
      pop: 2
      set: ignored-tail-outer

  cmd-set-quoted-prompt-unquoted-value-outer:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.unquoted.dosbatch
    - match: \"(?=[^"]*$)
      scope: punctuation.definition.prompt.end.dosbatch
      pop: 1
      set: ignored-tail-inner
    - match: \"
      scope: meta.string.dosbatch string.unquoted.dosbatch
      set: cmd-set-quoted-prompt-unquoted-value-inner
    - include: cmd-set-value-outer-common

###[ COMMAND SET /P VARIABLE=PROMPT ]#########################################

  cmd-set-unquoted-prompt:
    # SET variable
    # SET variable=unquoted prompt
    # SET variable="quoted prompt"
    - match: (?=\S)
      set:
        - cmd-set-prompt-meta
        - cmd-set-unquoted-prompt-variable-outer

  cmd-set-unquoted-prompt-variable-inner:
    - meta_include_prototype: false
    - meta_content_scope: variable.other.readwrite.dosbatch
    - match: =
      scope: keyword.operator.assignment.dosbatch
      set: cmd-set-unquoted-prompt-value-inner-start
    - match: \"
      scope: variable.other.readwrite.dosbatch
      set: cmd-set-unquoted-prompt-variable-outer
    - include: cmd-set-variable-inner-common

  cmd-set-unquoted-prompt-variable-outer:
    - meta_include_prototype: false
    - meta_content_scope: variable.other.readwrite.dosbatch
    - match: =
      scope: keyword.operator.assignment.dosbatch
      set: cmd-set-unquoted-prompt-value-outer-start
    - match: \"
      scope: variable.other.readwrite.dosbatch
      set: cmd-set-unquoted-prompt-variable-inner
    - include: cmd-set-variable-outer-common


  cmd-set-unquoted-prompt-value-inner-start:
    - include: quoted-eol-pop
    - match: \"
      scope:
        meta.string.dosbatch string.quoted.double.dosbatch
        punctuation.definition.string.begin.dosbatch
      set: cmd-set-unquoted-prompt-quoted-value-outer
    - match: (?=\S)
      set: cmd-set-unquoted-prompt-unquoted-value-inner

  cmd-set-unquoted-prompt-value-outer-start:
    - include: unquoted-eol-pop
    - match: \"
      scope:
        meta.string.dosbatch string.quoted.double.dosbatch
        punctuation.definition.string.begin.dosbatch
      set: cmd-set-unquoted-prompt-quoted-value-inner
    - match: (?=\S)
      set: cmd-set-unquoted-prompt-unquoted-value-outer


  cmd-set-unquoted-prompt-quoted-value-inner:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.quoted.double.dosbatch
    # match the very last quotation mark
    - match: (?={{set_quoted_end}})
      branch_point: cmd-set-unquoted-prompt-inner
      branch:
        - cmd-set-unquoted-prompt-quoted-value-inner-continue
        - cmd-set-unquoted-prompt-quoted-value-inner-end
    - match: \"
      scope: meta.string.dosbatch string.quoted.double.dosbatch
      set: cmd-set-unquoted-prompt-quoted-value-outer
    - include: cmd-set-value-inner-common

  cmd-set-unquoted-prompt-quoted-value-inner-continue:
    - meta_include_prototype: false
    - match: \"
      set: cmd-set-unquoted-prompt-quoted-value-inner-nextline

  cmd-set-unquoted-prompt-quoted-value-inner-nextline:
    - meta_include_prototype: false
    - match: '{{set_quoted_end}}'
      scope: punctuation.definition.string.end.dosbatch
      pop: 2
      set: ignored-tail-inner
    - match: '{{eoc}}'
      fail: cmd-set-unquoted-prompt-inner
    - include: quoted-escapes-and-interpolations
    - include: line-continuations

  cmd-set-unquoted-prompt-quoted-value-inner-end:
    - meta_include_prototype: false
    - match: \"
      scope: punctuation.definition.string.end.dosbatch
      pop: 2
      set: ignored-tail-outer

  cmd-set-unquoted-prompt-quoted-value-outer:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.quoted.double.dosbatch
    - match: '{{set_quoted_end}}'
      scope:
        meta.string.dosbatch string.quoted.double.dosbatch
        punctuation.definition.string.end.dosbatch
      pop: 1
      set: ignored-tail-inner
    - match: \"
      scope: meta.string.dosbatch string.quoted.double.dosbatch
      set: cmd-set-unquoted-prompt-quoted-value-inner
    - include: cmd-set-value-outer-common


  cmd-set-unquoted-prompt-unquoted-value-inner:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.unquoted.dosbatch
    - match: \"
      scope: meta.string.dosbatch string.unquoted.dosbatch
      set: cmd-set-unquoted-prompt-unquoted-value-outer
    - include: cmd-set-value-inner-common

  cmd-set-unquoted-prompt-unquoted-value-outer:
    - meta_include_prototype: false
    - meta_content_scope: meta.string.dosbatch string.unquoted.dosbatch
    - match: \"
      scope: meta.string.dosbatch string.unquoted.dosbatch
      set: cmd-set-unquoted-prompt-unquoted-value-inner
    - include: cmd-set-value-outer-common

###[ COMMAND TITLE ]##########################################################

  cmd-title:
    - match: (?i:title){{keyword_break}}
      scope:
        meta.function-call.identifier.dosbatch
        support.function.builtin.dosbatch
      push: cmd-title-args

  cmd-title-args:
    - meta_scope: meta.command.title.dosbatch
    - meta_content_scope: meta.function-call.arguments.dosbatch
    - include: eoc-pop
    - match: \s*((/)\?){{arg_break}}
      captures:
        1: variable.parameter.option.help.dosbatch
        2: punctuation.definition.variable.dosbatch
      push: ignored-tail-outer
    - match: \s*(?!\^\n)
      set: cmd-title-output

  cmd-title-output:
    - meta_content_scope:
        meta.command.title.dosbatch
        meta.function-call.arguments.dosbatch
        meta.string.dosbatch string.unquoted.dosbatch
    - include: redirection-interpolations
    - include: unquoted-words
    - include: eoc-pop

###[ OPERATORS ]##############################################################

  operators:
    - match: \@
      scope: keyword.operator.at.dosbatch
      push: maybe-label
    - match: \&\&?|\|\|
      scope: keyword.operator.logical.dosbatch
    - match: \|
      scope: keyword.operator.assignment.pipe.dosbatch
      push: maybe-illegal-comment

  separator-comma:
    - match: ','
      scope: punctuation.separator.comma.dosbatch

  separator-semicolon:
    - match: ';'
      scope: punctuation.separator.semicolon.dosbatch

  invalid-operators:
    - match: '[|&<>]+'
      scope: invalid.illegal.operator.dosbatch

###[ CONSTANTS ]##############################################################

  constants:
    - match: \b(?i:nul){{keyword_break}}
      scope: constant.language.null.dosbatch

  numbers:
    - match: \b(0[xX])(\h*)\b
      captures:
        0: meta.number.integer.hexadecimal.dosbatch
        1: constant.numeric.base.dosbatch
        2: constant.numeric.value.dosbatch
    - match: ([-+]|\b)(0)([0-7]+)\b
      captures:
        0: meta.number.integer.octal.dosbatch
        1: keyword.operator.arithmetic.dosbatch
        2: constant.numeric.base.dosbatch
        3: constant.numeric.value.dosbatch
    - match: ([-+]|\b)(?:0|[1-9][0-9]*)\b
      captures:
        0: meta.number.integer.decimal.dosbatch constant.numeric.value.dosbatch
        1: keyword.operator.arithmetic.dosbatch

  number:
    - match: \b(0[xX])(\h*)\b
      captures:
        0: meta.number.integer.hexadecimal.dosbatch
        1: constant.numeric.base.dosbatch
        2: constant.numeric.value.dosbatch
      pop: 1
    - match: ([-+]|\b)(0)([0-7]+)\b
      captures:
        0: meta.number.integer.octal.dosbatch
        1: keyword.operator.arithmetic.dosbatch
        2: constant.numeric.base.dosbatch
        3: constant.numeric.value.dosbatch
      pop: 1
    - match: ([-+]|\b)(?:0|[1-9][0-9]*)\b
      captures:
        0: meta.number.integer.decimal.dosbatch constant.numeric.value.dosbatch
        1: keyword.operator.arithmetic.dosbatch
      pop: 1

###[ PATH PATTERNS ]##########################################################

  redirection-interpolations:
    - match: (\d)?(<&?|>[&>]?)
      captures:
        1: meta.number.integer.decimal.dosbatch
           constant.numeric.value.dosbatch
        2: keyword.operator.assignment.redirection.dosbatch
      push:
        - redirection-interpolation-meta
        - redirection-path

  redirection-interpolation-meta:
    - clear_scopes: 1
    - meta_include_prototype: false
    - meta_scope: meta.redirection.dosbatch
    - include: immediately-pop

  redirections:
    - match: (\d)?(<&?|>[&>]?)
      captures:
        1: meta.number.integer.decimal.dosbatch
           constant.numeric.value.dosbatch
        2: keyword.operator.assignment.redirection.dosbatch
      push:
        - redirection-meta
        - redirection-path

  redirection-meta:
    - meta_include_prototype: false
    - meta_scope: meta.redirection.dosbatch
    - include: immediately-pop

  redirection-path:
    - match: (?i:nul){{keyword_break}}
      scope: constant.language.null.dosbatch
      pop: 1
    - match: \d\b
      scope: meta.number.integer.decimal.dosbatch constant.numeric.value.dosbatch
      pop: 1
    - include: path-pattern

  path-pattern:
    - include: unquoted-eol-pop
    - match: \"
      scope: punctuation.definition.string.begin.dosbatch
      set:
        - path-pattern-quoted-body
        - path-pattern-relative
    - match: (?=\S)
      set:
        - path-pattern-unquoted-body
        - path-pattern-relative

  path-pattern-quoted-body:
    - meta_scope: meta.path.dosbatch meta.string.dosbatch string.quoted.double.dosbatch
    - include: string-end
    - include: path-pattern-quoted-content

  path-pattern-quoted-content:
    - include: quoted-escapes-and-interpolations
    - include: path-pattern-common

  path-pattern-unquoted-body:
    - meta_scope: meta.path.dosbatch meta.string.dosbatch string.unquoted.dosbatch
    - include: path-pattern-unquoted-end
    - include: path-pattern-unquoted-content

  path-pattern-unquoted-content:
    - include: unquoted-escapes-and-interpolations
    - include: path-pattern-common

  path-pattern-unquoted-end:
    - match: '{{path_terminators}}'
      pop: 1
    - include: line-continuations

  path-pattern-common:
    - match: '[:\\/]'
      scope: punctuation.separator.path.dosbatch
      push: path-pattern-relative
    - match: \.\.?(?=[\\/])
      scope: constant.other.path.parent.dosbatch
    - match: \.
      scope: punctuation.separator.path.dosbatch
    - match: \*
      scope: constant.other.wildcard.asterisk.dosbatch
    - match: \?
      scope: constant.other.wildcard.questionmark.dosbatch

  path-pattern-relative:
    - match: \.\.(?=[\\/])
      scope: constant.other.path.parent.dosbatch
      pop: 1
    - match: \.(?=[\\/])
      scope: constant.other.path.self.dosbatch
      pop: 1
    - include: immediately-pop

###[ STRINGS ]################################################################

  strings:
    - match: \"(?=.*\")
      scope: punctuation.definition.string.begin.dosbatch
      push: string-common

  string-common:
    - meta_include_prototype: false
    - meta_scope: meta.string.dosbatch string.quoted.double.dosbatch
    - include: string-end
    - include: quoted-escapes-and-interpolations

  string-end:
    - match: \"
      scope: punctuation.definition.string.end.dosbatch
      pop: 1
    - include: quoted-eol-pop

  escaped-characters:
    - match: \^.
      scope: constant.character.escape.dosbatch

  escaped-variables:
    - match: '%%'
      scope: constant.character.escape.dosbatch

###[ WORDS ]##################################################################

  quoted-words:
    - match: \"
      push: quoted-word-body

  quoted-word-body:
    # echo "text" outputs quotation marks no matter where they are located.
    # Hence no `punctuation` nor `string.quoted` scopes are applied.
    # This context is needed as a limited set of escaped chars and no line
    # continuation operators are supported within quotes.
    - match: \"
      pop: 1
    - match: $
      pop: 2
    - include: quoted-escapes-and-interpolations

  unquoted-words:
    - match: '{{unquoted_word_begin}}'
      push: unquoted-word-body

  unquoted-word-body:
    - include: unquoted-word-end
    - include: unquoted-escapes-and-interpolations

  unquoted-word-end:
    - match: '{{unquoted_word_break}}'
      pop: 1
    - include: line-continuations

###[ INTERPOLATIONS ]#########################################################

  unquoted-escapes-and-interpolations:
    - include: escaped-characters
    - include: quoted-escapes-and-interpolations

  quoted-escapes-and-interpolations:
    - include: escaped-variables
    - include: parameter-interpolations
    - include: expansion-interpolations
    - include: delayed-interpolations

  parameter-interpolations:
    - match: '{{parameter}}'
      scope:
        meta.interpolation.dosbatch
        variable.parameter.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch
      push: clear-string-scope

  expansion-interpolations:
    - match: '{{expansion_begin}}'
      scope:
        meta.interpolation.dosbatch
        punctuation.section.interpolation.begin.dosbatch
      push:
        - clear-string-scope
        - variable-expansion-name

  delayed-interpolations:
    - match: '{{delayed_expansion_begin}}'
      scope:
        meta.interpolation.dosbatch
        punctuation.section.interpolation.begin.dosbatch
      push:
        - clear-string-scope
        - variable-delayed-name

  clear-string-scope:
    - clear_scopes: 1
    - meta_include_prototype: false
    - include: immediately-pop

###[ VARIABLES ]##############################################################

  variables:
    - include: escaped-variables
    - include: variable-parameters
    - include: variable-expansions
    - include: variable-delayed

  variable-parameters:
    - match: '{{parameter}}'
      scope:
        meta.interpolation.dosbatch
        variable.parameter.option.dosbatch
      captures:
        1: punctuation.definition.variable.dosbatch

  variable-expansions:
    - match: '{{expansion_begin}}'
      scope:
        meta.interpolation.dosbatch
        punctuation.section.interpolation.begin.dosbatch
      push: variable-expansion-name

  variable-expansion-name:
    - meta_include_prototype: false
    - meta_content_scope: meta.interpolation.dosbatch
    - include: variable-expansion-end
    - include: variable-names
    - match: ':~'
      scope:
        meta.interpolation.substring.dosbatch
        punctuation.separator.dosbatch
      set: variable-expansion-substring
    - match: ':'
      scope:
        meta.interpolation.substitution.dosbatch
        punctuation.separator.dosbatch
      set: variable-expansion-substitution-pattern

  variable-expansion-substring:
    - meta_include_prototype: false
    - meta_content_scope: meta.interpolation.substring.dosbatch
    - include: variable-expansion-end
    - include: variable-delayed
    - include: variable-substrings

  variable-expansion-substitution-pattern:
    - meta_include_prototype: false
    - meta_content_scope: meta.interpolation.substitution.pattern.dosbatch
    - include: variable-expansion-end
    - include: variable-delayed
    - match: =
      scope:
        meta.interpolation.substitution.dosbatch
        keyword.operator.asignment.dosbatch
      set: variable-expansion-substitution-replacement
    - match: '[^=%!]+'
      scope: string.unquoted.dosbatch

  variable-expansion-substitution-replacement:
    - meta_include_prototype: false
    - meta_content_scope: meta.interpolation.substitution.replacement.dosbatch
    - include: variable-expansion-end
    - include: variable-delayed
    - match: '[^%!]+'
      scope: string.unquoted.dosbatch

  variable-expansion-end:
    - match: '%'
      scope:
        meta.interpolation.dosbatch
        punctuation.section.interpolation.end.dosbatch
      pop: 1

  variable-delayed:
    - match: '{{delayed_expansion_begin}}'
      scope:
        meta.interpolation.dosbatch
        punctuation.section.interpolation.begin.dosbatch
      push: variable-delayed-name

  variable-delayed-name:
    - meta_include_prototype: false
    - meta_content_scope: meta.interpolation.dosbatch
    - include: variable-delayed-end
    - include: variable-names
    - match: ':~'
      scope:
        meta.interpolation.substring.dosbatch
        punctuation.separator.dosbatch
      set: variable-delayed-substring
    - match: ':'
      scope:
        meta.interpolation.substitution.dosbatch
        punctuation.separator.dosbatch
      set: variable-delayed-substitution-pattern

  variable-delayed-substring:
    - meta_include_prototype: false
    - meta_content_scope: meta.interpolation.substring.dosbatch
    - include: variable-delayed-end
    - include: variable-expansions
    - include: variable-substrings

  variable-delayed-substitution-pattern:
    - meta_include_prototype: false
    - meta_content_scope: meta.interpolation.substitution.pattern.dosbatch
    - include: variable-delayed-end
    - include: variable-expansions
    - match: =
      scope:
        meta.interpolation.substitution.dosbatch
        keyword.operator.asignment.dosbatch
      set: variable-delayed-substitution-replacement
    - match: '[^=%!]+'
      scope: string.unquoted.dosbatch

  variable-delayed-substitution-replacement:
    - meta_include_prototype: false
    - meta_content_scope: meta.interpolation.substitution.replacement.dosbatch
    - include: variable-delayed-end
    - include: variable-expansions
    - match: '[^%!]+'
      scope: string.unquoted.dosbatch

  variable-delayed-end:
    - match: '!'
      scope:
        meta.interpolation.dosbatch
        punctuation.section.interpolation.end.dosbatch
      pop: 1

  variable-names:
    - match: (?i:errorlevel)\b
      scope: variable.language.dosbatch
    - match: '[^:~%!]+'
      scope: variable.other.readwrite.dosbatch

  variable-substrings:
    - include: separator-comma
    - match: ([-+]?)(0|[1-9][0-9]*)
      scope: meta.number.integer.decimal.dosbatch
      captures:
        1: keyword.operator.arithmetic.dosbatch
        2: constant.numeric.value.dosbatch
    # Note: This makes the whole expansion being handled as plain text.
    - match: ':[^%!{{redir_or_eoc_char}}]*'
      scope: invalid.illegal.unexpected.dosbatch

###[ ILLEGALS ]###############################################################

  expect-eoc:
    - include: eoc-pop
    - match: \S
      scope: invalid.illegal.expect-end-of-command.dosbatch

  maybe-illegal-comment:
    - match: '{{label_comment}}.*$\n?'
      scope: invalid.illegal.unexpected.dosbatch
      pop: 1
    - include: else-pop

  illegal-token-pop:
    - include: unquoted-eol-pop
    - match: \S+
      scope: invalid.illegal.unexpected.dosbatch
      set: else-pop

###[ PROTOTYPES ]#############################################################

  bol-pop:
    - meta_include_prototype: false
    - match: ^
      pop: 1

  else-pop:
    - include: unquoted-eol-pop
    - match: (?=\S)
      pop: 1

  immediately-pop:
    - match: ''
      pop: 1

  line-continuations:
    - match: (\^)\n
      captures:
        1: punctuation.separator.continuation.line.dosbatch
      push: line-continuation-body

  line-continuation-body:
    - meta_include_prototype: false
    # The first linefeed diretly following a continuation is treated literal.
    # Note: Must consume \n to work.
    - match: ^\n
      set: bol-pop
    # The first caret at a continued line is escaped by the continuation caret
    # and thus printed literal.
    - match: ^\^?
      pop: 1

  eoc-pop:
    # Note: An end of command appears in unquoted regions only!
    #       `&` or `|` are literals in quoted words.
    - match: '{{eoc}}'
      pop: 1
    - include: line-continuations

  quoted-eol-pop:
    - match: $
      pop: 1

  unquoted-eol-pop:
    - include: quoted-eol-pop
    - include: line-continuations

  unquoted-var-pop:
    - match: '{{redir_or_eoc}}'
      pop: 1
    - include: line-continuations
