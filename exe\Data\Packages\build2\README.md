# Sublime Text: build2 Support Package

This package provides basic support for the `build2` toolchain.
Currently, it consists of syntax highlighting schemes for buildfile
files, manifest files, and repository list manifest files.

## Install
### Package Control
1. Open the command pallete and go to `Package Control: Install Package`.
2. Search for `build2` and type enter.
3. Wait for the installation to finish and you are done.

### Manual
1. Open the command pallete and go to `Preferences: Browse Packages`.
2. Clone [this repository](https://github.com/build2/build2-sublime-text) into the given folder with the name `build2`. 
```
git clone https://github.com/build2/build2-sublime-text.git build2
```
3. Wait for the download to finish and you are done.

## License
[MIT](LICENSE)
