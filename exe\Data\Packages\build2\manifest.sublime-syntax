%YAML 1.2
---
name: build2 manifest
file_extensions:
  - manifest
scope: source.build2.manifest
contexts:
  main:
    - match: '^([\s]*)#'
      scope: punctuation.definition.comment.build2.manifest
      push: line_comment
    - match: ':'
      scope: punctuation.definition.variable.build2.manifest
      push: format_version
    - match: '\b(name|project)\b'
      scope: variable.language.build2.manifest
      set: [name, define]
    - match: version
      scope: variable.language.build2.manifest
      set: [version, define]
    - match: priority
      scope: variable.language.build2.manifest
      set: [priority, define]
    - match: '\b(summary)\b'
      scope: variable.language.build2.manifest
      set: [summary, define]
    - match: license
      scope: variable.language.build2.manifest
      push: [license, define]
    - match: '\b(topics|keywords)\b'
      scope: variable.language.build2.manifest
      push: [topics, define]
    - match: description-file
      scope: variable.language.build2.manifest
      push: [description-file, define]
    - match: description-type
      scope: variable.language.build2.manifest
      push: [description-type, define]
    - match: description
      scope: variable.language.build2.manifest
      push: [description, define]
    - match: changes-file
      scope: variable.language.build2.manifest
      push: [changes-file, define]
    - match: changes
      scope: variable.language.build2.manifest
      push: [changes, define]
    - match: '\b(url|doc-url|src-url|package-url|email|package-email|build-email|build-warning-email|build-error-email)\b'
      scope: variable.language.build2.manifest
      push: [web, define]
    - match: depends
      scope: variable.language.build2.manifest
      push: [depends, define]
    - match: requires
      scope: variable.language.build2.manifest
      push: [requires, define]
    - match: '\b(tests|examples|benchmarks)\b'
      scope: variable.language.build2.manifest
      push: [tests_examples_benchmarks, define]
    - match: builds
      scope: variable.language.build2.manifest
      push: [builds, define]
    - match: '\b(build-include|build-exclude)\b'
      scope: variable.language.build2.manifest
      push: [build-config, define]
    - match: '\b[^:\s]+\b'
      scope: source.build2.manifest
      push: [other_variable, define]

  line_comment:
    - meta_scope: comment.line.build2.manifest
    - match: $
      pop: true

  define:
    - match: '(:)(\\\n)'
      set: [main, multi_line_mode]
      captures:
        1: punctuation.definition.variable.build2.manifest
        2: constant.character.escape.build2.manifest
    - match: ':'
      scope: punctuation.definition.variable.build2.manifest
      pop: true

  line_mode:
    - match: '\\\\'
      scope: constant.character.escape.build2.manifest
    - match: '\\\n'
      scope: constant.character.escape.build2.manifest
    - match: '\n'
      pop: true

  multi_line_mode:
    - meta_scope: text.build2.manifest
    - match: '\\\\'
      scope: constant.character.escape.build2.manifest
    - match: '^\\\n'
      scope: constant.character.escape.build2.manifest
      pop: true
    - match: '^<EOF>$'
      scope: constant.character.escape.build2.manifest
      pop: true
    - match: '\\\n'
      scope: constant.character.escape.build2.manifest

  documentation_comments:
    - match: '\\;'
      scope: constant.character.escape.build2.manifest
    - match: ';'
      scope: punctuation.definition.comment.line.documentation.build2.manifest
      push: documentation_line_comment
  documentation_line_comment:
    - meta_scope: comment.line.documentation.build2.manifest
    - match: $
      pop: true

  other_variable:
    - include: line_mode
    - include: documentation_comments

  format_version:
    - match: '\b[0-9]*\b'
      scope: constant.numeric.build2.manifest
    - match: $
      pop: true

  name:
    - include: line_mode
    - match: '\b(?!(build|con|prn|aux|nul|com1|com2|com3|com4|com5|com6|com7|com8|com9|lpt1|lpt2|lpt3|lpt4|lpt5|lpt6|lpt7|lpt8|lpt9)[\b\s])(([a-zA-Z])([a-zA-Z0-9._+-]*)([a-zA-Z0-9+]))'
      scope: entity.name.build2.manifest

  version:
    - include: line_mode
    - match: '\b((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)\b'
      scope: constant.numeric.build2.manifest

  priority:
    - include: line_mode
    - include: documentation_comments
    - match: '\b(security|high|medium|low)\b'
      scope: constant.language.build2.manifest

  summary:
    - include: line_mode

  license:
    - include: line_mode
    - include: documentation_comments
    - match: '\b(MIT|BSD2|BSD3|BSD4|GPLv2|GPLv3|LGPLv2.1|LGPLv2|LGPLv3|AGPLv2|AGPLv3|ASLv1.1|ASLv1|ASLv2|MPLv2|public domain|available source|proprietary|TODO)\b'
      scope: constant.language.build2.manifest
    - match: '\b(0BSD|AAL|Abstyles|Adobe-2006|Adobe-Glyph|ADSL|AFL-1.1|AFL-1.2|AFL-2.0|AFL-2.1|AFL-3.0|Afmparse|AGPL-1.0-only|AGPL-1.0-or-later|AGPL-3.0-only|AGPL-3.0-or-later|Aladdin|AMDPLPA|AML|AMPAS|ANTLR-PD|ANTLR-PD-fallback|Apache-1.0|Apache-1.1|Apache-2.0|APAFML|APL-1.0|APSL-1.0|APSL-1.1|APSL-1.2|APSL-2.0|Artistic-1.0|Artistic-1.0-cl8|Artistic-1.0-Perl|Artistic-2.0|Bahyph|Barr|Beerware|BitTorrent-1.0|BitTorrent-1.|blessing|BlueOak-1.0.0|Borceux|BSD-1-Clause|BSD-2-Clause|BSD-2-Clause-Patent|BSD-2-Clause-Views|BSD-3-Clause|BSD-3-Clause-Attribution|BSD-3-Clause-Clear|BSD-3-Clause-LBNL|BSD-3-Clause-No-Nuclear-License|BSD-3-Clause-No-Nuclear-License-2014|BSD-3-Clause-No-Nuclear-Warranty|BSD-3-Clause-Open-MPI|BSD-4-Clause|BSD-4-Clause-UC|BSD-Protection|BSD-Source-Code|BSL-1.0|BUSL-1.1|bzip2-1.0.5|bzip2-1.0.6|CAL-1.0|CAL-1.0-Combined-Work-Exception|Caldera|CATOSL-1.1|CC-BY-1.0|CC-BY-2.0|CC-BY-2.5|CC-BY-3.0|CC-BY-3.0-AT|CC-BY-3.0-US|CC-BY-4.0|CC-BY-NC-1.0|CC-BY-NC-2.0|CC-BY-NC-2.5|CC-BY-NC-3.0|CC-BY-NC-4.0|CC-BY-NC-ND-1.0|CC-BY-NC-ND-2.0|CC-BY-NC-ND-2.5|CC-BY-NC-ND-3.0|CC-BY-NC-ND-3.0-IGO|CC-BY-NC-ND-4.0|CC-BY-NC-SA-1.0|CC-BY-NC-SA-2.0|CC-BY-NC-SA-2.5|CC-BY-NC-SA-3.0|CC-BY-NC-SA-4.0|CC-BY-ND-1.0|CC-BY-ND-2.0|CC-BY-ND-2.5|CC-BY-ND-3.0|CC-BY-ND-4.0|CC-BY-SA-1.0|CC-BY-SA-2.0|CC-BY-SA-2.0-UK|CC-BY-SA-2.5|CC-BY-SA-3.0|CC-BY-SA-3.0-AT|CC-BY-SA-4.0|CC-PDDC|CC0-1.0|CDDL-1.0|CDDL-1.1|CDLA-Permissive-1.0|CDLA-Sharing-1.0|CECILL-1.0|CECILL-1.1|CECILL-2.0|CECILL-2.1|CECILL-B|CECILL-C|CERN-OHL-1.1|CERN-OHL-1.2|CERN-OHL-P-2.0|CERN-OHL-S-2.0|CERN-OHL-W-2.0|ClArtistic|CNRI-Jython|CNRI-Python|CNRI-Python-GPL-Compatible|Condor-1.1|copyleft-next-0.3.0|copyleft-next-0.3.1|CPAL-1.0|CPL-1.0|CPOL-1.02|Crossword|CrystalStacker|CUA-OPL-1.0|Cube|curl|D-FSL-1.0|diffmark|DOC|Dotseqn|DSDP|dvipdfm|ECL-1.0|ECL-2.0|EFL-1.0|EFL-2.0|eGenix|Entessa|EPICS|EPL-1.0|EPL-2.0|ErlPL-1.1|etalab-2.0|EUDatagrid|EUPL-1.0|EUPL-1.1|EUPL-1.2|Eurosym|Fair|Frameworx-1.0|FreeImage|FSFAP|FSFUL|FSFULLR|FTL|GFDL-1.1-invariants-only|GFDL-1.1-invariants-or-later|GFDL-1.1-no-invariants-only|GFDL-1.1-no-invariants-or-later|GFDL-1.1-only|GFDL-1.1-or-later|GFDL-1.2-invariants-only|GFDL-1.2-invariants-or-later|GFDL-1.2-no-invariants-only|GFDL-1.2-no-invariants-or-later|GFDL-1.2-only|GFDL-1.2-or-later|GFDL-1.3-invariants-only|GFDL-1.3-invariants-or-later|GFDL-1.3-no-invariants-only|GFDL-1.3-no-invariants-or-later|GFDL-1.3-only|GFDL-1.3-or-later|Giftware|GL2PS|Glide|Glulxe|GLWTPL|gnuplot|GPL-1.0-only|GPL-1.0-or-later|GPL-2.0-only|GPL-2.0-or-later|GPL-3.0-only|GPL-3.0-or-later|gSOAP-1.3b|HaskellReport|Hippocratic-2.1|HPND|HPND-sell-variant|HTMLTIDY|IBM-pibs|ICU|IJG|ImageMagick|iMatix|Imlib2|Info-ZIP|Intel|Intel-ACPI|Interbase-1.0|IPA|IPL-1.0|ISC|JasPer-2.0|JPNIC|JSON|LAL-1.2|LAL-1.3|Latex2e|Leptonica|LGPL-2.0-only|LGPL-2.0-or-later|LGPL-2.1-only|LGPL-2.1-or-later|LGPL-3.0-only|LGPL-3.0-or-later|LGPLLR|Libpng|libpng-2.0|libselinux-1.0|libtiff|LiLiQ-P-1.1|LiLiQ-R-1.1|LiLiQ-Rplus-1.1|Linux-OpenIB|LPL-1.0|LPL-1.02|LPPL-1.0|LPPL-1.1|LPPL-1.2|LPPL-1.3a|LPPL-1.3c|MakeIndex|MirOS|MIT|MIT-0|MIT-advertising|MIT-CMU|MIT-enna|MIT-feh|MIT-open-group|MITNFA|Motosoto|mpich2|MPL-1.0|MPL-1.1|MPL-2.0|Mpl-2.0-no-copyleft-exception|MS-PL|MS-Rl|Mtll|mulanpsl-1.0|mulanpsl-2.0|Multics|Mup|NASA-1.3|Naumen|NBPL-1.0|NCGL-UK-2.0|NCSA|Net-SNMP|NetCDF|Newsletr|NGPL|NIST-PD|NIST-PD-fallback|NLOD-1.0|NLPL|Nokia|NOSL|Noweb|NPL-1.0|NPL-1.1|NPOSL-3.0|NRL|NTP|NTP-0|O-UDA-1.0|OCCT-PL|OCLC-2.0|ODbL-1.0|ODC-By-1.0|OFL-1.0|OFL-1.0-no-RFN|OFL-1.0-RFN|OFL-1.1|OFL-1.1-no-RFN|OFL-1.1-RFN|OGC-1.0|OGL-Canada-2.0|OGL-UK-1.0|OGL-UK-2.0|OGL-UK-3.0|OGTSL|OLDAP-1.1|OLDAP-1.2|OLDAP-1.3|OLDAP-1.4|OLDAP-2.0|OLDAP-2.0.1|OLDAP-2.1|OLDAP-2.2|OLDAP-2.2.1|OLDAP-2.2.2|OLDAP-2.3|OLDAP-2.4|OLDAP-2.5|OLDAP-2.6|OLDAP-2.7|OLDAP-2.8|OML|OpenSSL|OPL-1.0|OSET-PL-2.1|OSL-1.0|OSL-1.1|OSL-2.0|OSL-2.1|OSL-3.0|Parity-6.0.0|Parity-7.0.0|PDDL-1.0|PHP-3.0|PHP-3.01|Plexus|PolyForm-Noncommercial-1.0.0|PolyForm-Small-Business-1.0.0|PostgreSQL|PSF-2.0|psfrag|psutils|Python-2.0|Qhull|QPL-1.0|Rdisc|RHeCos-1.1|RPL-1.1|RPL-1.5|RPSL-1.0|RSA-MD|RSCPL|Ruby|SAX-PD|Saxpath|SCEA|Sendmail|Sendmail-8.23|SGI-B-1.0|SGI-B-1.1|SGI-B-2.0|SHL-0.5|SHL-0.51|SimPL-2.0|SISSL|SISSL-1.2|Sleepycat|SMLNJ|SMPPL|SNIA|Spencer-86|Spencer-94|Spencer-99|SPL-1.0|SSH-OpenSSH|SSH-short|SSPL-1.0|SugarCRM-1.1.3|SWL|TAPR-OHL-1.0|TCL|TCP-wrappers|TMate|TORQUE-1.1|TOSL|TU-Berlin-1.0|TU-Berlin-2.0|UCL-1.0|Unicode-DFS-2015|Unicode-DFS-2016|Unicode-TOU|Unlicense|UPL-1.0|Vim|VOSTROM|VSL-1.0|W3C|W3C-19980720|W3C-20150513|Watcom-1.0|Wsuipa|WTFPL|X11|Xerox|XFree86-1.1|xinetd|Xnet|xpp|XSkat|PL-1.0|PL-1.1|Zed|Zend-2.0|Zimbra-1.3|Zimbra-1.4|Zlib|zlib-acknowledgement|ZPL-1.1|ZPL-2.0|ZPL-2.1)\b'
      scope: constant.language.build2.manifest
    - match: '\b(other|AND|OR)\b'
      scope: keyword.control.build2.manifest

  topics:
    - include: line_mode

  description-file:
    - include: line_mode
    - include: documentation_comments

  description-type:
    - include: line_mode
    - match: '\b(text/plain|text/markdown;variant=GFM|text/markdown;variant=CommonMark)\b'
      scope: constant.language.build2.manifest

  description:
    - include: line_mode

  changes-file:
    - include: line_mode
    - include: documentation_comments

  changes:
    - include: line_mode

  web:
    - include: line_mode
    - include: documentation_comments

  depends:
    - include: line_mode
    - include: documentation_comments
    - match: '(==|<=|>=|[?*~\^<>|])'
      scope: keyword.operator.build2.manifest
    - match: '\('
      scope: punctuation.section.parens.begin.build2.manifest
    - match: '\)'
      scope: punctuation.section.parens.end.build2.manifest
    - match: '\['
      scope: punctuation.section.brackets.begin.build2.manifest
    - match: '\]'
      scope: punctuation.section.brackets.end.build2.manifest
    - match: '\b(?!(build|con|prn|aux|nul|com1|com2|com3|com4|com5|com6|com7|com8|com9|lpt1|lpt2|lpt3|lpt4|lpt5|lpt6|lpt7|lpt8|lpt9)[\b\s])(([a-zA-Z])([a-zA-Z0-9._+-]*)([a-zA-Z0-9+]))'
      scope: entity.name.build2.manifest
    - match: '\b((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)\b'
      scope: constant.numeric.build2.manifest

  requires:
    - include: line_mode
    - include: documentation_comments
    - match: '(==|<=|>=|[?~\^<>|])'
      scope: keyword.operator.build2.manifest
    - match: '\b(c\+\+98|c\+\+03|c\+\+11|c\+\+14|c\+\+17|c\+\+20|c\+\+23|posix|linux|macos|macosx|freebsd|windows)\b'
      scope: constant.language.build2.manifest
    - match: '\b(gcc|clang)(_[0-9](.[0-9](.[0-9])?)?)?\b'
      scope: constant.language.build2.manifest
    - match: '\bmsvc(_[0-9][0-9a-zA-Z]*)?\b'
      scope: constant.language.build2.manifest
    # - match: '\b(?!(build|con|prn|aux|nul|com1|com2|com3|com4|com5|com6|com7|com8|com9|lpt1|lpt2|lpt3|lpt4|lpt5|lpt6|lpt7|lpt8|lpt9)[\b\s])(([a-zA-Z])([a-zA-Z0-9._+-]*)([a-zA-Z0-9+]))'
    #   scope: entity.name.build2.manifest
    - match: '\b((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)\b'
      scope: constant.numeric.build2.manifest

  tests_examples_benchmarks:
    - include: line_mode
    - include: documentation_comments
    - match: '(==|<=|>=|[?*~\^<>|$])'
      scope: keyword.operator.build2.manifest
    - match: '\b((([0-9]+)\.([0-9]+)\.([0-9]+)(?:-([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?)\b'
      scope: constant.numeric.build2.manifest

  builds:
    - include: line_mode
    - include: documentation_comments
    - match: '(|<=|>=|[:&!+|-])'
      scope: keyword.operator.build2.manifest
    - match: '\('
      scope: punctuation.section.parens.begin.build2.manifest
    - match: '\)'
      scope: punctuation.section.parens.end.build2.manifest
    - match: '\b(all|default|none|experimental|legacy|optimized|gcc|clang|linux|macos|windows)\b'
      scope: constant.language.build2.manifest

  build-config:
    - include: line_mode
    - include: documentation_comments
