<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>csv</string>
		<string>tsv</string>
	</array>
	<key>name</key>
	<string>Advanced CSV</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>begin</key>
			<string>(\")</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>string.quoted.double.advanced_csv</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\")</string>
			<key>name</key>
			<string>meta.quoted.advanced_csv</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(\[([+-]?\d*)(\:)?([+-]?\d*)(\,)?([+-]?\d*)(\:)?([+-]?\d*)\])?\s*([&lt;&gt;v^])?\s*(=)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.operator.advanced_csv</string>
				</dict>
				<key>10</key>
				<dict>
					<key>name</key>
					<string>keyword.operator.advanced_csv</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.formula.advanced_csv</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.formula.advanced_csv</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.formula.advanced_csv</string>
				</dict>
				<key>8</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.formula.advanced_csv</string>
				</dict>
				<key>9</key>
				<dict>
					<key>name</key>
					<string>keyword.operator.advanced_csv</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?=(\")|$)</string>
			<key>name</key>
			<string>meta.range.advanced_csv</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>source.python</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.advanced_csv</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(?&lt;=^|,|\s|\")([0-9.eE+-]+)(?=$|,|\s|\")</string>
			<key>name</key>
			<string>meta.number.advanced_csv</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.advanced_csv</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(?&lt;=^|,|\s|\")([^, \t\"]+)(?=$|,|\s|\")</string>
			<key>name</key>
			<string>meta.nonnumber.advanced_csv</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.operator.advanced_csv</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(\,)</string>
			<key>name</key>
			<string>meta.delimiter.advanced_csv</string>
		</dict>
	</array>
	<key>scopeName</key>
	<string>text.advanced_csv</string>
	<key>uuid</key>
	<string>7ce133ea-e34b-47d9-a03c-341293337c88</string>
</dict>
</plist>
