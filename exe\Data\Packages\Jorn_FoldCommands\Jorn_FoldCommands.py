import sublime
import sublime_plugin
import re


class IndentationFoldDetector:
    """Detects indentation levels and calculates fold regions."""

    def __init__(self, view):
        self.view = view
        self.indent_blocks = []
        self.fold_regions = []
        self.settings = sublime.load_settings('Jorn_FoldCommands.sublime-settings')

    def get_line_indentation(self, line_text):
        """Calculate the indentation level of a line."""
        if not line_text.strip():  # Empty or whitespace-only line
            return None

        # Count leading whitespace
        indent_chars = len(line_text) - len(line_text.lstrip())

        # Convert to indentation level based on tab size
        tab_size = self.view.settings().get('tab_size', 4)
        translate_tabs_to_spaces = self.view.settings().get('translate_tabs_to_spaces', True)

        # Debug output
        debug_mode = self.settings.get('debug_mode', False)
        if debug_mode:
            print(f"Line: '{line_text[:50]}...' | Indent chars: {indent_chars} | Tab size: {tab_size}")

        if translate_tabs_to_spaces:
            # Using spaces - divide by tab_size
            level = indent_chars // tab_size
        else:
            # Using tabs - count tabs and convert remaining spaces
            tabs = line_text[:indent_chars].count('\t')
            spaces = indent_chars - tabs
            level = tabs + (spaces // tab_size)

        if debug_mode:
            print(f"  -> Indentation level: {level}")

        return level

    def detect_indent_blocks(self):
        """Detect all indentation blocks in the document."""
        self.indent_blocks = []

        content = self.view.substr(sublime.Region(0, self.view.size()))
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line_region = self.view.line(self.view.text_point(i, 0))
            indent_level = self.get_line_indentation(line)

            # Skip empty lines
            if indent_level is None:
                continue

            # Get a preview of the line content
            content_preview = line.strip()[:50]

            self.indent_blocks.append({
                'line': i,
                'level': indent_level,
                'content': content_preview,
                'region': line_region,
                'full_line': line
            })

        return self.indent_blocks
    
    def calculate_fold_regions(self, target_level=None):
        """Calculate fold regions for indentation blocks at or below the target level."""
        if not self.indent_blocks:
            self.detect_indent_blocks()

        self.fold_regions = []

        for i, block in enumerate(self.indent_blocks):
            # If target_level is specified, only process blocks at that level or below
            if target_level is not None and block['level'] < target_level:
                continue

            # Find the start of the content (after the current line)
            content_start = block['region'].end() + 1

            # Find the end of the content (before next block of same or lower indentation level)
            content_end = self.view.size()

            for j in range(i + 1, len(self.indent_blocks)):
                next_block = self.indent_blocks[j]
                if next_block['level'] <= block['level']:
                    content_end = next_block['region'].begin() - 1
                    break

            # Only create fold region if there's content to fold
            if content_start < content_end:
                fold_region = sublime.Region(content_start, content_end)
                self.fold_regions.append({
                    'block': block,
                    'region': fold_region,
                    'level': block['level']
                })

        return self.fold_regions

    def get_regions_by_level(self, level):
        """Get all fold regions for a specific indentation level."""
        if not self.fold_regions:
            self.calculate_fold_regions()

        return [fr for fr in self.fold_regions if fr['level'] == level]

    def get_regions_at_or_below_level(self, level):
        """Get all fold regions for indentation at or below the specified level."""
        if not self.fold_regions:
            self.calculate_fold_regions()

        return [fr for fr in self.fold_regions if fr['level'] >= level]


class JornFoldCommandsSelectByLevelCommand(sublime_plugin.TextCommand):
    """Select all content that would be folded at a specific indentation level."""

    def run(self, edit, level=1):
        detector = IndentationFoldDetector(self.view)
        fold_regions = detector.get_regions_by_level(level)

        if not fold_regions:
            sublime.status_message(f"No foldable content found at indentation level {level}")
            return

        # Clear current selection and select all fold regions
        self.view.sel().clear()
        for fold_region in fold_regions:
            self.view.sel().add(fold_region['region'])

        sublime.status_message(f"Selected {len(fold_regions)} regions at indentation level {level}")


class JornFoldCommandsSelectAtOrBelowLevelCommand(sublime_plugin.TextCommand):
    """Select all content that would be folded at or below a specific indentation level."""

    def run(self, edit, level=1):
        detector = IndentationFoldDetector(self.view)
        fold_regions = detector.get_regions_at_or_below_level(level)

        if not fold_regions:
            sublime.status_message(f"No foldable content found at or below indentation level {level}")
            return

        # Clear current selection and select all fold regions
        self.view.sel().clear()
        for fold_region in fold_regions:
            self.view.sel().add(fold_region['region'])

        sublime.status_message(f"Selected {len(fold_regions)} regions at or below indentation level {level}")


class JornFoldCommandsShowIndentBlocksCommand(sublime_plugin.TextCommand):
    """Show all detected indentation blocks in the console for debugging."""

    def run(self, edit):
        detector = IndentationFoldDetector(self.view)
        blocks = detector.detect_indent_blocks()

        if not blocks:
            sublime.status_message("No indentation blocks found")
            return

        print("\n=== Detected Indentation Blocks ===")
        for i, block in enumerate(blocks):
            indent = "  " * block['level']
            print(f"{i+1:2d}. {indent}Level {block['level']}: {block['content']} (line {block['line']+1})")

        print(f"\nTotal blocks found: {len(blocks)}")
        sublime.status_message(f"Found {len(blocks)} indentation blocks - check console for details")


class JornFoldCommandsDebugIndentationCommand(sublime_plugin.TextCommand):
    """Debug indentation detection for the current file."""

    def run(self, edit):
        detector = IndentationFoldDetector(self.view)

        # Enable debug mode temporarily
        original_debug = detector.settings.get('debug_mode', False)
        detector.settings.set('debug_mode', True)

        print("\n=== Debug Indentation Detection ===")
        print(f"File: {self.view.file_name() or 'Untitled'}")
        print(f"Tab size: {self.view.settings().get('tab_size', 4)}")
        print(f"Translate tabs to spaces: {self.view.settings().get('translate_tabs_to_spaces', True)}")
        print()

        blocks = detector.detect_indent_blocks()

        # Restore original debug setting
        detector.settings.set('debug_mode', original_debug)

        if not blocks:
            print("No indentation blocks detected!")
            sublime.status_message("No indentation blocks detected - check console for details")
        else:
            print(f"\nSummary: Found {len(blocks)} indentation blocks")
            sublime.status_message(f"Debug complete - found {len(blocks)} blocks - check console")


class JornFoldCommandsShowFoldRegionsCommand(sublime_plugin.TextCommand):
    """Show all calculated fold regions in the console for debugging."""

    def run(self, edit, level=None):
        detector = IndentationFoldDetector(self.view)

        if level is not None:
            fold_regions = detector.get_regions_by_level(level)
            title = f"Fold Regions at Indentation Level {level}"
        else:
            fold_regions = detector.calculate_fold_regions()
            title = "All Fold Regions"

        if not fold_regions:
            sublime.status_message(f"No fold regions found")
            return

        print(f"\n=== {title} ===")
        for i, fold_region in enumerate(fold_regions):
            block = fold_region['block']
            region = fold_region['region']
            content_preview = self.view.substr(region)[:50].replace('\n', '\\n')

            print(f"{i+1:2d}. Level {block['level']}: {block['content']}")
            print(f"    Region: {region.begin()}-{region.end()} ({region.size()} chars)")
            print(f"    Preview: {content_preview}...")
            print()

        print(f"Total fold regions: {len(fold_regions)}")
        sublime.status_message(f"Found {len(fold_regions)} fold regions - check console for details")
