import sublime
import sublime_plugin
import re


class MarkdownFoldDetector:
    """Detects markdown heading levels and calculates fold regions."""
    
    def __init__(self, view):
        self.view = view
        self.headings = []
        self.fold_regions = []
    
    def detect_headings(self):
        """Detect all markdown headings in the document."""
        self.headings = []
        
        # ATX style headings: # Heading, ## Heading, etc.
        atx_pattern = r'^(#{1,6})\s+(.*)$'
        
        # Setext style headings: Heading followed by === or ---
        setext_h1_pattern = r'^(.+)\n={3,}\s*$'
        setext_h2_pattern = r'^(.+)\n-{3,}\s*$'
        
        content = self.view.substr(sublime.Region(0, self.view.size()))
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_region = self.view.line(self.view.text_point(i, 0))
            
            # Check ATX style headings
            atx_match = re.match(atx_pattern, line)
            if atx_match:
                level = len(atx_match.group(1))
                title = atx_match.group(2).strip()
                self.headings.append({
                    'line': i,
                    'level': level,
                    'title': title,
                    'region': line_region,
                    'type': 'atx'
                })
                continue
            
            # Check Setext style headings (need to look ahead)
            if i < len(lines) - 1:
                next_line = lines[i + 1]
                
                # H1 style (===)
                if re.match(r'^={3,}\s*$', next_line) and line.strip():
                    title = line.strip()
                    # Include both the heading line and underline
                    start_region = self.view.text_point(i, 0)
                    end_region = self.view.line(self.view.text_point(i + 1, 0)).end()
                    heading_region = sublime.Region(start_region, end_region)
                    
                    self.headings.append({
                        'line': i,
                        'level': 1,
                        'title': title,
                        'region': heading_region,
                        'type': 'setext_h1'
                    })
                    continue
                
                # H2 style (---)
                if re.match(r'^-{3,}\s*$', next_line) and line.strip():
                    title = line.strip()
                    # Include both the heading line and underline
                    start_region = self.view.text_point(i, 0)
                    end_region = self.view.line(self.view.text_point(i + 1, 0)).end()
                    heading_region = sublime.Region(start_region, end_region)
                    
                    self.headings.append({
                        'line': i,
                        'level': 2,
                        'title': title,
                        'region': heading_region,
                        'type': 'setext_h2'
                    })
                    continue
        
        return self.headings
    
    def calculate_fold_regions(self, target_level=None):
        """Calculate fold regions for headings at or below the target level."""
        if not self.headings:
            self.detect_headings()
        
        self.fold_regions = []
        
        for i, heading in enumerate(self.headings):
            # If target_level is specified, only process headings at that level or below
            if target_level is not None and heading['level'] < target_level:
                continue
            
            # Find the start of the content (after the heading)
            content_start = heading['region'].end() + 1
            
            # Find the end of the content (before next heading of same or higher level)
            content_end = self.view.size()
            
            for j in range(i + 1, len(self.headings)):
                next_heading = self.headings[j]
                if next_heading['level'] <= heading['level']:
                    content_end = next_heading['region'].begin() - 1
                    break
            
            # Only create fold region if there's content to fold
            if content_start < content_end:
                fold_region = sublime.Region(content_start, content_end)
                self.fold_regions.append({
                    'heading': heading,
                    'region': fold_region,
                    'level': heading['level']
                })
        
        return self.fold_regions
    
    def get_regions_by_level(self, level):
        """Get all fold regions for a specific heading level."""
        if not self.fold_regions:
            self.calculate_fold_regions()
        
        return [fr for fr in self.fold_regions if fr['level'] == level]
    
    def get_regions_at_or_below_level(self, level):
        """Get all fold regions for headings at or below the specified level."""
        if not self.fold_regions:
            self.calculate_fold_regions()
        
        return [fr for fr in self.fold_regions if fr['level'] >= level]


class JornFoldCommandsSelectByLevelCommand(sublime_plugin.TextCommand):
    """Select all content that would be folded at a specific heading level."""
    
    def run(self, edit, level=2):
        detector = MarkdownFoldDetector(self.view)
        fold_regions = detector.get_regions_by_level(level)
        
        if not fold_regions:
            sublime.status_message(f"No foldable content found at heading level {level}")
            return
        
        # Clear current selection and select all fold regions
        self.view.sel().clear()
        for fold_region in fold_regions:
            self.view.sel().add(fold_region['region'])
        
        sublime.status_message(f"Selected {len(fold_regions)} regions at heading level {level}")


class JornFoldCommandsSelectAtOrBelowLevelCommand(sublime_plugin.TextCommand):
    """Select all content that would be folded at or below a specific heading level."""
    
    def run(self, edit, level=2):
        detector = MarkdownFoldDetector(self.view)
        fold_regions = detector.get_regions_at_or_below_level(level)
        
        if not fold_regions:
            sublime.status_message(f"No foldable content found at or below heading level {level}")
            return
        
        # Clear current selection and select all fold regions
        self.view.sel().clear()
        for fold_region in fold_regions:
            self.view.sel().add(fold_region['region'])
        
        sublime.status_message(f"Selected {len(fold_regions)} regions at or below heading level {level}")


class JornFoldCommandsShowHeadingsCommand(sublime_plugin.TextCommand):
    """Show all detected headings in the console for debugging."""
    
    def run(self, edit):
        detector = MarkdownFoldDetector(self.view)
        headings = detector.detect_headings()
        
        if not headings:
            sublime.status_message("No headings found")
            return
        
        print("\n=== Detected Markdown Headings ===")
        for i, heading in enumerate(headings):
            indent = "  " * (heading['level'] - 1)
            print(f"{i+1:2d}. {indent}H{heading['level']}: {heading['title']} (line {heading['line']+1}, {heading['type']})")
        
        print(f"\nTotal headings found: {len(headings)}")
        sublime.status_message(f"Found {len(headings)} headings - check console for details")


class JornFoldCommandsShowFoldRegionsCommand(sublime_plugin.TextCommand):
    """Show all calculated fold regions in the console for debugging."""
    
    def run(self, edit, level=None):
        detector = MarkdownFoldDetector(self.view)
        
        if level is not None:
            fold_regions = detector.get_regions_by_level(level)
            title = f"Fold Regions at Level {level}"
        else:
            fold_regions = detector.calculate_fold_regions()
            title = "All Fold Regions"
        
        if not fold_regions:
            sublime.status_message(f"No fold regions found")
            return
        
        print(f"\n=== {title} ===")
        for i, fold_region in enumerate(fold_regions):
            heading = fold_region['heading']
            region = fold_region['region']
            content_preview = self.view.substr(region)[:50].replace('\n', '\\n')
            
            print(f"{i+1:2d}. H{heading['level']}: {heading['title']}")
            print(f"    Region: {region.begin()}-{region.end()} ({region.size()} chars)")
            print(f"    Preview: {content_preview}...")
            print()
        
        print(f"Total fold regions: {len(fold_regions)}")
        sublime.status_message(f"Found {len(fold_regions)} fold regions - check console for details")
