<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<!--
Humble Night
======================================================================
 A minimalistic dark color theme for Sublime Text theme by weop.

 Copyright (c) 2016 weop. https://github.com/weop
 Released under the MIT License <http://opensource.org/licenses/MIT>
-->
<plist version="1.0">
<dict>
    <key>name</key>
    <string>Humble Night</string>
    <key>settings</key>
    <array>
        <dict>
            <key>settings</key>
            <dict>
                <key>background</key>
                <string>#0f0f10</string>
                <key>caret</key>
                <string>#f8f8f0</string>
                <key>foreground</key>
                <string>#ccc</string>
                <key>invisibles</key>
                <string>#3b3a32</string>
                <key>lineHighlight</key>
                <string>#1B2126</string>
                <key>selection</key>
                <string>#5AC480</string>
                <key>selectionForeground</key>
                <string>#ffffff</string>
                <key>findHighlight</key>
                <string>#5AC480</string>
                <key>findHighlightForeground</key>
                <string>#eee</string>
                <key>selectionBorder</key>
                <string>#222218</string>
                <key>activeGuide</key>
                <string>#9d550fb0</string>

                <key>bracketsForeground</key>
                <string>#eee</string>
                <key>bracketsOptions</key>
                <string>underline</string>

                <key>bracketContentsForeground</key>
                <string>#8C8C8C</string>
                <key>bracketContentsOptions</key>
                <string>underline</string>

                <key>tagsForeground</key>
                <string>#5AC480</string>
                <key>tagsOptions</key>
                <string>underline</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Comment</string>
            <key>scope</key>
            <string>comment</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#333940</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>String</string>
            <key>scope</key>
            <string>string</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#5AC480</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Number</string>
            <key>scope</key>
            <string>constant.numeric</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ecf0f1</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Built-in constant</string>
            <key>scope</key>
            <string>constant.language</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#3498db</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>User-defined constant</string>
            <key>scope</key>
            <string>constant.character, constant.other</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#9b59b6</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Variable</string>
            <key>scope</key>
            <string>variable</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#559BD3</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Keyword</string>
            <key>scope</key>
            <string>keyword</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ecf0f1</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Storage</string>
            <key>scope</key>
            <string>storage</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#559BD3</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Storage type</string>
            <key>scope</key>
            <string>storage.type</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#D85653</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Class name</string>
            <key>scope</key>
            <string>entity.name.class</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string>underline</string>
                <key>foreground</key>
                <string>#EFCC48</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Inherited class</string>
            <key>scope</key>
            <string>entity.other.inherited-class</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ECD377</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Function name</string>
            <key>scope</key>
            <string>entity.name.function</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#EFCC48</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Function argument</string>
            <key>scope</key>
            <string>variable.parameter</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#D85653</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Tag name</string>
            <key>scope</key>
            <string>entity.name.tag</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#687482</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Tag attribute</string>
            <key>scope</key>
            <string>entity.other.attribute-name</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#687482</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Tag Punctuations</string>
            <key>scope</key>
            <string>punctuation.definition.tag.begin, punctuation.definition.tag.end</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#687482</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Library function</string>
            <key>scope</key>
            <string>support.function</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#559BD3</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Library constant</string>
            <key>scope</key>
            <string>support.constant</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string></string>
                <key>foreground</key>
                <string>#559BD3</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Library class/type</string>
            <key>scope</key>
            <string>support.type, support.class</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#1abc9c</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Library variable</string>
            <key>scope</key>
            <string>support.other.variable</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#559BD3</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Invalid</string>
            <key>scope</key>
            <string>invalid</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#DE3932</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Invalid deprecated</string>
            <key>scope</key>
            <string>invalid.deprecated</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#DE3932</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>JSON String</string>
            <key>scope</key>
            <string>meta.structure.dictionary.json string.quoted.double.json</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ffffff</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>diff.header</string>
            <key>scope</key>
            <string>meta.diff, meta.diff.header</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#75715E</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>diff.deleted</string>
            <key>scope</key>
            <string>markup.deleted</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#e74c3c</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>diff.inserted</string>
            <key>scope</key>
            <string>markup.inserted</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#1abc9c</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>diff.changed</string>
            <key>scope</key>
            <string>markup.changed</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#3498db</string>
            </dict>
        </dict>

        <dict>
            <key>scope</key>
            <string>constant.numeric.line-number.find-in-files - match</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#8FBE00A</string>
            </dict>
        </dict>
        <dict>
            <key>scope</key>
            <string>entity.name.filename.find-in-files</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#E6DB74</string>
            </dict>
        </dict>
        <dict>
            <key>scope</key>
            <string>keyword.other</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ecf0f1</string>
            </dict>
        </dict>
        <dict>
            <key>scope</key>
            <string>meta.property-value, support.constant.property-value, constant.other.color</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ffffff</string>
            </dict>
        </dict>
        <dict>
            <key>scope</key>
            <string>meta.structure.dictionary.json string.quoted.double.json</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#8C8C8C</string>
            </dict>
        </dict>
        <dict>
            <key>scope</key>
            <string>meta.structure.dictionary.value.json string.quoted.double.json</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ffffff</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Function argument</string>
            <key>scope</key>
            <string>meta.property-name support.type.property-name</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string>normal</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Function argument</string>
            <key>scope</key>
            <string>meta.property-value punctuation.separator.key-value</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#B0B0B0</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Function argument</string>
            <key>scope</key>
            <string>keyword.other.use, keyword.other.function.use, keyword.other.namespace, keyword.other.new, keyword.other.special-method, keyword.other.unit, keyword.other.use-as</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#C4C4C4</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Function argument</string>
            <key>scope</key>
            <string>meta.use support.class.builtin, meta.other.inherited-class support.class.builtin</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#EFCC48</string>
                <key>fontStyle</key>
                <string>normal</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Function argument</string>
            <key>scope</key>
            <string>variable.other</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#E75F64</string>
                <key>fontStyle</key>
                <string>normal</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>Coffeescript Function argument</string>
            <key>scope</key>
            <string>variable.parameter.function.coffee</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#B0B0B0</string>
                <key>fontStyle</key>
                <string>italic</string>
            </dict>
        </dict>

        <!-- Markdown support. -->
        <dict>
            <key>name</key>
            <string>Markdown Titles</string>
            <key>scope</key>
            <string>entity.name.section.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#8C8C8C</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Markdown Title Hash</string>
            <key>scope</key>
            <string>punctuation.definition.heading.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#eeeeee</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Markdown Raw</string>
            <key>scope</key>
            <string>markup.raw.inline.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ffffff</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Markdown bold stars</string>
            <key>scope</key>
            <string>punctuation.definition.bold.markdown, punctuation.definition.italic.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#eeeeee</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Markdown link title braces</string>
            <key>scope</key>
            <string>punctuation.definition.string.begin.markdown, punctuation.definition.string.end.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#eeeeee</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Markdown link braces</string>
            <key>scope</key>
            <string>punctuation.definition.metadata.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#eeeeee</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Markdown link</string>
            <key>scope</key>
            <string>markup.underline.link.markdown, markup.underline.link.image.markdown, meta.image.inline.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#C4C4C4</string>
                <key>fontStyle</key>
                <string>italic</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Markdown bold/italic</string>
            <key>scope</key>
            <string>markup.bold.markdown, markup.italic.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#C4C4C4</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Markdown bold/italic</string>
            <key>scope</key>
            <string>markup.italic.markdown</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string>italic</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Markdown bold/italic</string>
            <key>scope</key>
            <string>markup.bold.markdown</string>
            <key>settings</key>
            <dict>
                <key>fontStyle</key>
                <string>bold</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>Markdown pre</string>
            <key>scope</key>
            <string>markup.raw.block.markdown</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#00a8c6</string>
            </dict>
        </dict>

        <dict>
            <key>name</key>
            <string>GitGutter deleted</string>
            <key>scope</key>
            <string>markup.deleted.git_gutter</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#e74c3c</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>GitGutter inserted</string>
            <key>scope</key>
            <string>markup.inserted.git_gutter</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#1abc9c</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>GitGutter changed</string>
            <key>scope</key>
            <string>markup.changed.git_gutter</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#f1c40f</string>
            </dict>
        </dict>
        <dict>
            <key>name</key>
            <string>CSS Class</string>
            <key>scope</key>
            <string>entity.other.attribute-name.class.css</string>
            <key>settings</key>
            <dict>
                <key>foreground</key>
                <string>#ECD377</string>
            </dict>
        </dict>
    </array>
</dict>
</plist>
