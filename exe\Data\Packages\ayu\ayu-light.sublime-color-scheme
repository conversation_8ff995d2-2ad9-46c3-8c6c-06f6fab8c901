{"name": "ayu", "globals": {"background": "#fcfcfc", "foreground": "#5c6166", "invisibles": "#5c61664d", "caret": "#ffaa33", "block_caret": "#ffaa334d", "line_highlight": "#8a91991a", "accent": "#ffaa33", "popup_css": "\n      html, body {\n        background-color: #ffffff;\n        color: #5c6166;\n        --mdpopups-font-mono: \"PragmataPro Mono Liga\", \"sf mono\", Consolas, \"Liberation Mono\", Menlo, Courier, monospace;\n        --mdpopups-bg: #ffffff;\n        --mdpopups-link: #22a4e6;\n      }\n      body {\n        padding: 1px 3px;\n      }\n      a {\n        color: rgba(34,164,230, .7);\n      }\n    ", "gutter": "#fcfcfc", "gutter_foreground": "#8a919966", "line_diff_width": "2", "line_diff_added": "#6cbf43b3", "line_diff_modified": "#478accb3", "line_diff_deleted": "#ff7383b3", "selection": "#036dd626", "selection_border": "#036dd626", "selection_border_width": "1", "inactive_selection": "#5696d61a", "inactive_selection_border": "#5696d61a", "selection_corner_style": "round", "selection_corner_radius": "4", "highlight": "#ffaa3366", "find_highlight": "#ffaa33", "find_highlight_foreground": "#fcfcfc", "guide": "#8a91992e", "active_guide": "#8a919959", "stack_guide": "#8a919966", "shadow": "#fcfcfc4d", "shadow_width": "3"}, "rules": [{"name": "Comment", "scope": "comment", "font_style": "italic", "foreground": "#787b8099"}, {"name": "String", "scope": "string - meta.template, constant.other.symbol, string.quoted", "foreground": "#86b300"}, {"name": "Regular Expressions and Escape Characters", "scope": "string.regexp, constant.character, constant.other", "foreground": "#4cbf99"}, {"name": "Number", "scope": "constant.numeric", "foreground": "#ffaa33"}, {"name": "Built-in constants", "scope": "constant.language", "foreground": "#ffaa33"}, {"name": "Constants", "scope": "meta.constant, entity.name.constant", "foreground": "#a37acc"}, {"name": "Variable", "scope": "variable", "foreground": "#5c6166"}, {"name": "Member Variable", "scope": "variable.member", "foreground": "#f07171"}, {"name": "Language variable", "scope": "variable.language", "font_style": "italic", "foreground": "#55b4d4"}, {"name": "Storage", "scope": "storage, storage.type.keyword", "foreground": "#fa8d3e"}, {"name": "Keyword", "scope": "keyword", "foreground": "#fa8d3e"}, {"name": "Java keyword fixes", "scope": "source.java meta.class.java meta.class.identifier.java storage.type.java", "foreground": "#fa8d3e"}, {"name": "Operators", "scope": "keyword.operator", "foreground": "#ed9366"}, {"name": "Separators like ; or ,", "scope": "punctuation.separator, punctuation.terminator", "foreground": "#5c6166b3"}, {"name": "Punctuation", "scope": "punctuation.section", "foreground": "#5c6166"}, {"name": "Accessor", "scope": "punctuation.accessor", "foreground": "#ed9366"}, {"name": "JavaScript/TypeScript interpolation punctuation", "scope": "punctuation.definition.template-expression", "foreground": "#fa8d3e"}, {"name": "Ruby interpolation punctuation", "scope": "punctuation.section.interpolation", "foreground": "#fa8d3e"}, {"name": "Types fixes", "scope": "source.java storage.type, source.haskell storage.type, source.c storage.type, source.zig storage.type", "foreground": "#22a4e6"}, {"name": "Inherited class type", "scope": "entity.other.inherited-class", "foreground": "#55b4d4"}, {"name": "Lambda arrow", "scope": "storage.type.function", "foreground": "#fa8d3e"}, {"name": "Java primitive variable types", "scope": "source.java storage.type.primitive", "foreground": "#55b4d4"}, {"name": "Function name", "scope": "entity.name.function", "foreground": "#f2ae49"}, {"name": "Function arguments", "scope": "variable.parameter, meta.parameter", "foreground": "#a37acc"}, {"name": "Function call", "scope": "variable.function, variable.annotation, meta.function-call.generic, support.function.go", "foreground": "#f2ae49"}, {"name": "Library function", "scope": "support.function, support.macro", "foreground": "#f07171"}, {"name": "Imports and packages", "scope": "entity.name.import, entity.name.package", "foreground": "#86b300"}, {"name": "Entity name", "scope": "entity.name, source.js meta.function-call.constructor variable.type", "foreground": "#22a4e6"}, {"name": "Tag", "scope": "entity.name.tag, meta.tag.sgml", "foreground": "#55b4d4"}, {"name": "Tag start/end", "scope": "punctuation.definition.tag.end, punctuation.definition.tag.begin, punctuation.definition.tag", "foreground": "#55b4d480"}, {"name": "Tag attribute", "scope": "entity.other.attribute-name", "foreground": "#f2ae49"}, {"name": "Library constant", "scope": "support.constant", "font_style": "italic", "foreground": "#ed9366"}, {"name": "Library class/type", "scope": "support.type, support.class, source.go storage.type", "foreground": "#55b4d4"}, {"name": "Decorators/annotation", "scope": "meta.decorator variable.other, meta.decorator punctuation.decorator, storage.type.annotation, variable.annotation, punctuation.definition.annotation", "foreground": "#e6b673"}, {"name": "Invalid", "scope": "invalid", "foreground": "#e65050"}, {"name": "diff.header", "scope": "meta.diff, meta.diff.header", "foreground": "#c594c5"}, {"name": "Ruby class methods", "scope": "source.ruby variable.other.readwrite", "foreground": "#f2ae49"}, {"name": "CSS tag names", "scope": "source.css entity.name.tag, source.sass entity.name.tag, source.scss entity.name.tag, source.less entity.name.tag, source.stylus entity.name.tag", "foreground": "#22a4e6"}, {"name": "CSS browser prefix", "scope": "source.css support.type, source.sass support.type, source.scss support.type, source.less support.type, source.stylus support.type", "foreground": "#787b8099"}, {"name": "CSS Properties", "scope": "support.type.property-name", "font_style": "normal", "foreground": "#55b4d4"}, {"name": "Search Results Nums", "scope": "constant.numeric.line-number.find-in-files - match", "foreground": "#787b8099"}, {"name": "Search Results Match Nums", "scope": "constant.numeric.line-number.match", "foreground": "#fa8d3e"}, {"name": "Search Results Lines", "scope": "entity.name.filename.find-in-files", "foreground": "#86b300"}, {"scope": "message.error", "foreground": "#e65050"}, {"name": "<PERSON><PERSON> heading", "scope": "markup.heading, markup.heading entity.name", "font_style": "bold", "foreground": "#86b300"}, {"name": "Markup links", "scope": "markup.underline.link, string.other.link", "foreground": "#22a4e6"}, {"name": "<PERSON><PERSON> Italic", "scope": "markup.italic", "font_style": "italic", "foreground": "#f07171"}, {"name": "Markup Bold", "scope": "markup.bold", "font_style": "bold", "foreground": "#f07171"}, {"name": "Markup Bold/italic", "scope": "markup.italic markup.bold, markup.bold markup.italic", "font_style": "bold italic"}, {"name": "Markup Code", "scope": "markup.raw", "background": "#5c616605"}, {"name": "Markup Code Inline", "scope": "markup.raw.inline", "background": "#5c61660f"}, {"name": "Markdown Separator", "scope": "meta.separator", "font_style": "bold", "background": "#5c61660f", "foreground": "#787b8099"}, {"name": "<PERSON><PERSON>", "scope": "markup.quote", "foreground": "#4cbf99", "font_style": "italic"}, {"name": "Markup <PERSON> Bullet", "scope": "markup.list punctuation.definition.list.begin", "foreground": "#f2ae49"}, {"name": "<PERSON><PERSON> added", "scope": "markup.inserted", "foreground": "#6cbf43"}, {"name": "Mark<PERSON> modified", "scope": "markup.changed", "foreground": "#478acc"}, {"name": "Mark<PERSON> removed", "scope": "markup.deleted", "foreground": "#ff7383"}, {"name": "Markup Strike", "scope": "markup.strike", "foreground": "#e6b673"}, {"name": "Markup Table", "scope": "markup.table", "background": "#5c61660f", "foreground": "#55b4d4"}, {"name": "Markup Raw Inline", "scope": "text.html.markdown markup.inline.raw", "foreground": "#ed9366"}, {"name": "Markdown - Line Break", "scope": "text.html.markdown meta.dummy.line-break", "background": "#787b8099", "foreground": "#787b8099"}, {"name": "Markdown - Raw Block Fenced", "scope": "punctuation.definition.markdown", "background": "#5c6166", "foreground": "#787b8099"}]}