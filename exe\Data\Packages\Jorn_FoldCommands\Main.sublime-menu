[
    {
        // Ribbon-Menu: Preferences -> Package Settings
        "caption": "Preferences",
        "mnemonic": "n",
        "id": "preferences",
        "children":
        [
            {
                "caption": "Package Settings",
                "mnemonic": "P",
                "id": "package-settings",
                "children":
                [
                    {
                        "caption": "Jorn_FoldCommands",
                        "children":
                        [
                            {/* --------------------------------------------- */ "caption": "-"},
                            {
                                "caption": "Jorn_FoldCommands.py",
                                "command": "open_file",
                                "args": {"file": "${packages}/Jorn_FoldCommands/Jorn_FoldCommands.py"},
                            },
                            {/* --------------------------------------------- */ "caption": "-"},
                            {
                                "caption": "Settings",
                                "command": "open_file",
                                "args": {"file": "${packages}/Jorn_FoldCommands/Jorn_FoldCommands.sublime-settings"},
                            },
                            {
                                "caption": "Settings – User",
                                "command": "open_file",
                                "args": {"file": "${packages}/User/Jorn_FoldCommands.sublime-settings"},
                            },
                            {/* --------------------------------------------- */ "caption": "-"},
                            {
                                "caption": "Keymap",
                                "command": "open_file",
                                "args": {"file": "${packages}/Jorn_FoldCommands/Default (Windows).sublime-keymap"},
                            },
                            {
                                "caption": "Keymap – User",
                                "command": "open_file",
                                "args": {"file": "${packages}/User/Default (Windows).sublime-keymap"},
                            },
                            {/* --------------------------------------------- */ "caption": "-"},
                            {
                                "caption": "Commands",
                                "command": "open_file",
                                "args": {"file": "${packages}/Jorn_FoldCommands/Default.sublime-commands"},
                            },
                            {
                                "caption": "Commands – User",
                                "command": "open_file",
                                "args": {"file": "${packages}/User/Default.sublime-commands"},
                            },
                            {/* --------------------------------------------- */ "caption": "-"},
                            {
                                "caption": "Open Folder",
                                "command": "open_dir",
                                "args": {"dir": "${packages}/Jorn_FoldCommands"}
                            },
                            {/* --------------------------------------------- */ "caption": "-"},
                        ]
                    }
                ]
            }
        ]
    },
    {
        // Ribbon-Menu: Jorn Tools
        "caption": "Jorn Tools",
        "mnemonic": "J",
        "id": "jorn-tools",
        "children": [
            {
                "caption": "Jorn_FoldCommands",
                "children":
                [
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Show Indentation Blocks",
                        "command": "jorn_fold_commands_show_indent_blocks"
                    },
                    {
                        "caption": "Show Fold Regions",
                        "command": "jorn_fold_commands_show_fold_regions"
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Select Level 0 Content",
                        "command": "jorn_fold_commands_select_by_level",
                        "args": {"level": 0}
                    },
                    {
                        "caption": "Select Level 1 Content",
                        "command": "jorn_fold_commands_select_by_level",
                        "args": {"level": 1}
                    },
                    {
                        "caption": "Select Level 2 Content",
                        "command": "jorn_fold_commands_select_by_level",
                        "args": {"level": 2}
                    },
                    {
                        "caption": "Select Level 3 Content",
                        "command": "jorn_fold_commands_select_by_level",
                        "args": {"level": 3}
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Select Level 1+ Content",
                        "command": "jorn_fold_commands_select_at_or_below_level",
                        "args": {"level": 1}
                    },
                    {
                        "caption": "Select Level 2+ Content",
                        "command": "jorn_fold_commands_select_at_or_below_level",
                        "args": {"level": 2}
                    },
                    {
                        "caption": "Select Level 3+ Content",
                        "command": "jorn_fold_commands_select_at_or_below_level",
                        "args": {"level": 3}
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Settings",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_FoldCommands/Jorn_FoldCommands.sublime-settings"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                ]
            }
        ]
    }
]
